<?php

namespace App\Http\Controllers;

use App\Models\Payment;
use App\Models\ChequeStatement;
use App\Models\AccountSubGroup;
use App\Models\PaymentMethod;
use Illuminate\Http\Request;
use Carbon\Carbon;

class ProfitAndLossReportController extends Controller
{
    public function generateProfitAndLoss(Request $request)
    {
        $request->validate([
            'from_date' => 'required|date',
            'to_date' => 'required|date|after_or_equal:from_date',
        ]);

        $fromDate = Carbon::parse($request->from_date)->startOfDay();
        $toDate = Carbon::parse($request->to_date)->endOfDay();

        try {
            // Initialize income and expense arrays
            $income = [];
            $expenses = [];

            // Process PaymentMethod transactions (sales, invoices, and purchases)
            $paymentMethodTransactions = PaymentMethod::whereBetween('date', [$fromDate, $toDate])
                ->get();

            $incomeMainAccounts = ['Direct Income', 'Indirect Income', 'Sales Accounts'];
            $expenseMainAccounts = ['Direct Expenses', 'Indirect Expenses', 'Purchase Account'];

            foreach ($paymentMethodTransactions as $transaction) {
                $accountGroup = $this->getMainAccountType($transaction->account_group);
                $referenceNumber = $transaction->reference_number ?? 'N/A';

                if (strtolower($transaction->type) === 'sales' || strtolower($transaction->type) === 'invoice') {
                    // Add selling price to income
                    $income[] = [
                        'category' => 'Sales/Invoice',
                        'description' => ucfirst($transaction->type) . ": {$referenceNumber}",
                        'amount' => abs((float)$transaction->total),
                        'date' => $transaction->date->format('Y-m-d'),
                    ];
                } elseif (strtolower($transaction->type) === 'purchase') {
                    // Add purchase amount to expenses
                    $expenses[] = [
                        'category' => 'Purchase',
                        'description' => "Purchase: {$referenceNumber}",
                        'amount' => abs((float)$transaction->total),
                        'date' => $transaction->date->format('Y-m-d'),
                    ];
                }
            }

            // Additional logic to include selling price and buying cost from invoice and sales items
            // Only after opening stock is closed (assuming fromDate is after opening stock closure)

            // Process Invoices
            $invoices = \App\Models\Invoice::whereBetween('invoice_date', [$fromDate, $toDate])->get();
            foreach ($invoices as $invoice) {
                foreach ($invoice->items as $item) {
                    // Add selling price to income
                    // $income[] = [
                    //     'category' => 'Invoice Sales',
                    //     'description' => "Invoice No: {$invoice->invoice_no}, Product ID: {$item->product_id}",
                    //     'amount' => abs((float)$item->sales_price * $item->quantity),
                    //     'date' => $invoice->invoice_date->format('Y-m-d'),
                    // ];
                    // Add buying cost to expenses
                    $expenses[] = [
                        'category' => 'Cost of Goods Sold',
                        'description' => "Invoice No: {$invoice->invoice_no}, Product ID: {$item->product_id}",
                        'amount' => abs((float)$item->total_buying_cost),
                        'date' => $invoice->invoice_date->format('Y-m-d'),
                    ];
                }
            }

            // Process Sales
            $sales = \App\Models\Sale::whereBetween('created_at', [$fromDate, $toDate])->get();
            foreach ($sales as $sale) {
                foreach ($sale->items as $item) {
                    // Add selling price to income
                    // $income[] = [
                    //     'category' => 'Sales',
                    //     'description' => "Bill No: {$sale->bill_number}, Product ID: {$item->product_id}",
                    //     'amount' => abs((float)$item->unit_price * $item->quantity),
                    //     'date' => $sale->created_at->format('Y-m-d'),
                    // ];
                    // Add buying cost to expenses
                    // Assuming buying cost is fetched from product or productVariant
                    $buyingCost = 0;
                    if ($item->product) {
                        $buyingCost = $item->product->buying_cost * $item->quantity;
                    }
                    $expenses[] = [
                        'category' => 'Cost of Goods Sold',
                        'description' => "Bill No: {$sale->bill_number}, Product ID: {$item->product_id}",
                        'amount' => abs((float)$buyingCost),
                        'date' => $sale->created_at->format('Y-m-d'),
                    ];
                }
            }

            // Process Payment transactions (vouchers)
            $paymentTransactions = Payment::whereBetween('payment_date', [$fromDate, $toDate])
                ->get();

            foreach ($paymentTransactions as $payment) {
                $accountGroup = $this->getMainAccountType($payment->account_type ?? 'Direct Expenses');
                $subAccountName = $this->getSubAccountName($payment->account_type);
                $description = $subAccountName ?: $accountGroup;

                // --- EXISTING LOGIC FOR LEDGER VOUCHERS ---
                if (strpos($payment->voucher_no, 'REC-') === 0 && $payment->refer_type === 'Ledger' && in_array($accountGroup, $incomeMainAccounts)) {
                    $income[] = [
                        'category' => $accountGroup,
                        'description' => "Receive Voucher: {$description} ({$payment->voucher_no})",
                        'amount' => abs((float)$payment->amount),
                        'date' => $payment->payment_date ? $payment->payment_date->format('Y-m-d') : 'N/A',
                    ];
                } elseif (strpos($payment->voucher_no, 'PAY-') === 0 && $payment->refer_type === 'Ledger' && in_array($accountGroup, $expenseMainAccounts)) {
                    $expenses[] = [
                        'category' => $accountGroup,
                        'description' => "Payment Voucher: {$description} ({$payment->voucher_no})",
                        'amount' => abs((float)$payment->amount),
                        'date' => $payment->payment_date ? $payment->payment_date->format('Y-m-d') : 'N/A',
                    ];
                }

                // --- NEW LOGIC FOR DISCOUNTS ---
                // Receive Voucher to Customer
                if (strpos($payment->voucher_no, 'REC-') === 0 && $payment->refer_type === 'Customer' && $payment->discount > 0) {
                    if (strtolower($payment->payment_method) === 'cheque') {
                        // Check if declined
                        $declinedCheque = \App\Models\ChequeStatement::where('payment_id', $payment->id)->where('status', 'declined')->first();
                        if ($declinedCheque) {
                            $income[] = [
                                'category' => 'Discount (Declined Cheque)',
                                'description' => "Discount reversed from declined cheque to customer (Voucher: {$payment->voucher_no})",
                                'amount' => abs((float)$payment->discount),
                                'date' => $payment->payment_date ? $payment->payment_date->format('Y-m-d') : 'N/A',
                            ];
                        } else {
                            $expenses[] = [
                                'category' => 'Discount',
                                'description' => "Discount given to customer (Voucher: {$payment->voucher_no})",
                                'amount' => abs((float)$payment->discount),
                                'date' => $payment->payment_date ? $payment->payment_date->format('Y-m-d') : 'N/A',
                            ];
                        }
                    } else {
                        $expenses[] = [
                            'category' => 'Discount',
                            'description' => "Discount given to customer (Voucher: {$payment->voucher_no})",
                            'amount' => abs((float)$payment->discount),
                            'date' => $payment->payment_date ? $payment->payment_date->format('Y-m-d') : 'N/A',
                        ];
                    }
                }
                // Payment Voucher to Supplier
                if (strpos($payment->voucher_no, 'PAY-') === 0 && $payment->refer_type === 'Supplier' && $payment->discount > 0) {
                    if (strtolower($payment->payment_method) === 'cheque') {
                        // Check if declined
                        $declinedCheque = \App\Models\ChequeStatement::where('payment_id', $payment->id)->where('status', 'declined')->first();
                        // Always add to income
                        $income[] = [
                            'category' => 'Discount',
                            'description' => "Discount received from supplier (Voucher: {$payment->voucher_no})",
                            'amount' => abs((float)$payment->discount),
                            'date' => $payment->payment_date ? $payment->payment_date->format('Y-m-d') : 'N/A',
                        ];
                        // If declined, also add to expense
                        if ($declinedCheque) {
                            $expenses[] = [
                                'category' => 'Discount (Declined Cheque)',
                                'description' => "Discount reversed from declined cheque to supplier (Voucher: {$payment->voucher_no})",
                                'amount' => abs((float)$payment->discount),
                                'date' => $payment->payment_date ? $payment->payment_date->format('Y-m-d') : 'N/A',
                            ];
                        }
                    } else {
                        $income[] = [
                            'category' => 'Discount',
                            'description' => "Discount received from supplier (Voucher: {$payment->voucher_no})",
                            'amount' => abs((float)$payment->discount),
                            'date' => $payment->payment_date ? $payment->payment_date->format('Y-m-d') : 'N/A',
                        ];
                    }
                }
            }

            // Process ChequeStatement transactions (declined vouchers)
            $chequeTransactions = ChequeStatement::whereBetween('payment_date', [$fromDate, $toDate])
                ->where('status', 'declined')
                ->get();

            foreach ($chequeTransactions as $cheque) {
                $accountGroup = $this->getMainAccountType($cheque->account_group ?? 'Direct Expenses');
                $subAccountName = $this->getSubAccountName($cheque->account_group);
                $description = $subAccountName ?: $accountGroup;

                // Only include if refer_type is 'Ledger'
                if (($cheque->refer_type ?? null) === 'Ledger') {
                    if (strpos($cheque->voucher_no, 'PAY-') === 0 && in_array($accountGroup, $incomeMainAccounts)) {
                        $income[] = [
                            'category' => $accountGroup,
                            'description' => "Declined Payment Voucher: {$description} ({$cheque->voucher_no})",
                            'amount' => abs((float)$cheque->amount),
                            'date' => $cheque->payment_date ? $cheque->payment_date->format('Y-m-d') : 'N/A',
                        ];
                    } elseif (strpos($cheque->voucher_no, 'REC-') === 0 && in_array($accountGroup, $expenseMainAccounts)) {
                        $expenses[] = [
                            'category' => $accountGroup,
                            'description' => "Declined Receive Voucher: {$description} ({$cheque->voucher_no})",
                            'amount' => abs((float)$cheque->amount),
                            'date' => $cheque->payment_date ? $cheque->payment_date->format('Y-m-d') : 'N/A',
                        ];
                    }
                }
            }

            // Calculate totals
            $totalIncome = array_sum(array_column($income, 'amount'));
            $totalExpenses = array_sum(array_column($expenses, 'amount'));
            $netProfitLoss = $totalIncome - $totalExpenses;

            return response()->json([
                'success' => true,
                'data' => [
                    'income' => $income,
                    'expenses' => $expenses,
                ],
                'totals' => [
                    'totalIncome' => $totalIncome,
                    'totalExpenses' => $totalExpenses,
                    'netProfitLoss' => $netProfitLoss,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error generating profit and loss report: ' . $e->getMessage(),
            ], 500);
        }
    }

    private function getMainAccountTypes()
    {
        return [
            'Direct Expenses', 'Indirect Expenses', 'Indirect Income', 'Loan Liabilities',
            'Bank OD', 'Current Liabilities', 'Sundry Creditors', 'Capital Account',
            'Bank Accounts', 'Current Asset', 'Sundry Debtors', 'Fixed Asset', 'Stock in hand',
            'Purchase Account', 'Sales Accounts', 'Cash in Hand'
        ];
    }

    private function getMainAccountType($accountGroup)
    {
        $mainAccountGroups = $this->getMainAccountTypes();

        if (in_array($accountGroup, $mainAccountGroups)) {
            return $accountGroup;
        }

        $subGroup = AccountSubGroup::where('sub_group_name', $accountGroup)->first();
        return $subGroup ? $subGroup->main_group : 'Direct Expenses';
    }

    private function isDebitAccountType($mainAccountType)
    {
        $debitAccountTypes = [
            'Direct Expenses', 'Indirect Expenses', 'Bank Accounts',
            'Current Asset', 'Sundry Debtors', 'Fixed Asset', 'Stock in hand',
            'Purchase Account' ,'Cash in Hand'
        ];

        return in_array($mainAccountType, $debitAccountTypes);
    }

    private function getSubAccountName($accountGroup)
    {
        $subGroup = AccountSubGroup::where('sub_group_name', $accountGroup)->first();
        return $subGroup ? $subGroup->sub_group_name : null;
    }
}