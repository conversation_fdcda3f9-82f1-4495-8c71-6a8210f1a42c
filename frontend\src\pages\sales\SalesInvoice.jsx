import React, {
  useState,
  useEffect,
  useRef,
  useCallback,
  useMemo,
} from "react";
import Select from "react-select";
import { parse, parseISO } from "date-fns";
import { useAuth } from "../../context/NewAuthContext"; // if you have this context for user info

const customSelectStyles = {
  control: (provided, state) => ({
    ...provided,
    borderColor: state.isFocused ? "#2563eb" : "#d1d5db",
    boxShadow: state.isFocused ? "0 0 0 1px #2563eb" : "none",
    minHeight: "42px",
    borderRadius: "0.375rem",
    "&:hover": {
      borderColor: "#2563eb",
    },
    fontSize: "0.875rem",
  }),
  menu: (provided) => ({
    ...provided,
    zIndex: 9999,
    fontSize: "0.875rem",
  }),
  option: (provided, state) => ({
    ...provided,
    backgroundColor: state.isSelected
      ? "#2563eb"
      : state.isFocused
        ? "#e0e7ff"
        : "white",
    color: state.isSelected ? "white" : "black",
    cursor: "pointer",
  }),
  placeholder: (provided) => ({
    ...provided,
    color: "#6b7280",
  }),
  singleValue: (provided) => ({
    ...provided,
    color: "#111827",
  }),
};

const unifiedSelectStyles = (hasError) => ({
  control: (provided, state) => ({
    ...provided,
    borderColor: hasError ? "#ef4444" : state.isFocused ? "#3b82f6" : "#d1d5db",
    boxShadow: state.isFocused
      ? "0 0 0 1px #3b82f6"
      : hasError
        ? "0 0 0 1px #ef4444"
        : "none",
    "&:hover": { borderColor: hasError ? "#ef4444" : "#9ca3af" },
    minHeight: "42px",
    borderRadius: "0.375rem",
    fontSize: "0.875rem",
  }),
  menu: (provided) => ({ ...provided, zIndex: 9999, fontSize: "0.875rem" }),
  option: (provided, state) => ({
    ...provided,
    backgroundColor: state.isSelected
      ? "#dbeafe"
      : state.isFocused
        ? "#eff6ff"
        : "white",
    color: state.isSelected ? "#2563eb" : "black",
    cursor: "pointer",
  }),
  placeholder: (provided) => ({
    ...provided,
    color: "#6b7280",
  }),
  singleValue: (provided) => ({
    ...provided,
    color: "#111827",
  }),
});
import axios from "axios";
import { toast } from "react-toastify";
import { format } from "date-fns";

// ErrorBoundary component remains the same
class ErrorBoundary extends React.Component {
  state = { hasError: false };
  static getDerivedStateFromError() {
    return { hasError: true };
  }
  componentDidCatch(error, errorInfo) {
    console.error("ErrorBoundary caught an error:", error, errorInfo);
  }
  render() {
    if (this.state.hasError) {
      return (
        <div className="p-4 text-red-600 bg-red-100 border border-red-400 rounded">
          <h1 className="font-bold">Something went wrong.</h1>
          <p>Please refresh the page or contact support.</p>
        </div>
      );
    }
    return this.props.children;
  }
}

// Helper functions
const formatCurrency = (value) => {
  return new Intl.NumberFormat("en-LK", {
    style: "currency",
    currency: "LKR",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value);
};

const isDateWithinScheme = (invoiceDate, startDate, endDate) => {
  try {
    const invDate = new Date(invoiceDate);
    const start = startDate ? new Date(startDate) : null;
    const end = endDate ? new Date(endDate) : null;
    return (!start || invDate >= start) && (!end || invDate <= end);
  } catch (e) {
    console.error("Error checking date within scheme:", e);
    return false;
  }
};

const SalesInvoice = ({
  initialData,
  isEditMode,
  onGenerateInvoice,
  onCancel,
  onUpdateInvoice,
}) => {
  const draftKey = "salesInvoiceDraft";
  const formatDate = (date) => {
    try {
      return date
        ? format(new Date(date), "yyyy-MM-dd")
        : format(new Date(), "yyyy-MM-dd");
    } catch {
      return format(new Date(), "yyyy-MM-dd");
    }
  };
  const { user } = useAuth();

  const [footerDetails, setFooterDetails] = useState({
    preparedBy: user?.name || "",
    approvedBy: null,
  });

  const approvedByRef = React.useRef(null);
  const preparedByRef = React.useRef(null);

  // Remove incorrect ref overwrite
  // useEffect(() => {
  //   approvedByRef.current = footerDetails.approvedBy;
  // }, [footerDetails.approvedBy]);
  useEffect(() => {
    // No need to sync preparedByRef as it's a simple input value
  }, [footerDetails.preparedBy]);

  // New function to focus react-select approvedBy
  const focusApprovedBySelect = () => {
    if (
      approvedByRef.current &&
      typeof approvedByRef.current.focus === "function"
    ) {
      approvedByRef.current.focus();
    }
  };
  const [users, setUsers] = useState([]);
  const [isLoadingUsers, setIsLoadingUsers] = useState(false);
  const [errorUsers, setErrorUsers] = useState(null);

  useEffect(() => {
    const fetchUsers = async () => {
      setIsLoadingUsers(true);
      setErrorUsers(null);
      try {
        const response = await axios.get("/api/quotation-users");
        console.log("Fetched users:", response.data);
        setUsers(response.data);
      } catch (error) {
        setErrorUsers("Failed to load users");
      } finally {
        setIsLoadingUsers(false);
      }
    };
    fetchUsers();
  }, []);

  useEffect(() => {
    if (isEditMode && initialData) {
      // Prepare approvedBy option
      const approvedByOption = initialData.approvedBy
        ? { value: initialData.approvedBy, label: initialData.approvedBy }
        : null;

      // Add approvedByOption to users list if not present
      if (
        approvedByOption &&
        !users.some((user) => user.name === approvedByOption.value)
      ) {
        setUsers((prevUsers) => [
          ...prevUsers,
          { id: null, name: approvedByOption.value },
        ]);
      }

      setFooterDetails({
        preparedBy: initialData.preparedBy || "",
        approvedBy: approvedByOption,
      });
    }
  }, [isEditMode, initialData, users]);

  // Refs
  const formRef = useRef(null);
  const invoiceNoRef = useRef(null);
  const invoiceDateRef = useRef(null);
  const invoiceTimeRef = useRef(null);
  const customerNameRef = useRef(null);
  const customerAddressRef = useRef(null);
  const customerPhoneRef = useRef(null);
  const customerEmailRef = useRef(null);
  const purchaseMethodRef = useRef(null);
  const purchaseAmountRef = useRef(null);
  const redeemPointsRef = useRef(null);
  const taxPercentageRef = useRef(null);
  const itemRefs = useRef([]);

  const [quotations, setQuotations] = useState([]);
  const [selectedQuotation, setSelectedQuotation] = useState(null);
  const [quotationMenuIsOpen, setQuotationMenuIsOpen] = useState(false);
  const quotationSelectRef = useRef(null);


  const [selectedSupplier, setSelectedSupplier] = useState(null); 

  const chequeNoInputRef = useRef(null);
  const bankNameInputRef = useRef(null);
  const issueDateInputRef = useRef(null);

  // Helper to get default form state
  const getDefaultFormState = () => {
    return {
      invoice: {
        no: "",
        date: format(new Date(), "yyyy-MM-dd"),
        time: format(new Date(), "HH:mm"),
      },
      customer: { id: null, name: "", address: "", phone: "", email: "" },
      items: [],
      purchaseDetails: { method: "cash", amount: 0, taxPercentage: 0 },
      cheque_no: "",
      bank_name: "",
      issue_date: "",
      status: "unpaid",
      id: null,
    };
  };

  // State
  const [formData, setFormData] = useState(() => {
    const defaultState = getDefaultFormState();

    if (isEditMode && initialData) {
      return {
        ...defaultState,
        ...initialData,
        invoice: {
          ...defaultState.invoice,
          ...(initialData.invoice || {}),
          date: formatDate(initialData.invoice?.date),
        },
        customer: {
          ...defaultState.customer,
          id: initialData.customer?.id || null,
          name: initialData.customer?.name || "",
          address: initialData.customer?.address || "",
          phone: initialData.customer?.phone || "",
          email: initialData.customer?.email || "",
        },
        items: (initialData.items || []).map((item, idx) => {
          const qty = parseFloat(item.qty || item.quantity || 1);
          const salesPrice = parseFloat(
            item.sales_price !== undefined
              ? item.sales_price
              : item.salesPrice || 0
          );
          const buyingCost = parseFloat(
            item.buyingCost || item.buying_cost || 0
          );
          const unitPrice = parseFloat(item.unitPrice || item.unit_price || 0);
          const mrp = parseFloat(item.mrp || unitPrice);
          const stock = parseFloat(item.stock || 0);

          // Fix: Ensure description is set properly, fallback to product_name or "N/A"
          const description =
            item.description && item.description.trim() !== ""
              ? item.description
              : item.product_name || "N/A";

          return {
            ...item,
            id: item.id || Date.now() + idx,
            productId: item.productId || item.product_id || null,
            categoryId: item.categoryId || null,
            qty,
            unitPrice,
            mrp,
            salesPrice,
            buyingCost,
            stock,
            free: parseFloat(item.free || 0),
            discountAmount: parseFloat(
              item.discountAmount || item.discount_amount || 0
            ),
            discountPercentage: parseFloat(
              item.discountPercentage || item.discount_percentage || 0
            ),
            specialDiscount: parseFloat(
              item.specialDiscount || item.special_discount || 0
            ),
            total: parseFloat(
              item.total ||
                qty * salesPrice -
                  (item.discountAmount || item.discount_amount || 0)
            ),
            totalBuyingCost: parseFloat(
              item.totalBuyingCost || item.total_buying_cost || qty * buyingCost
            ),
            supplier: item.supplier || "",
            category: item.category || "",
            store_location: item.store_location || "",
            description,
          };
        }),
        purchaseDetails: {
          ...defaultState.purchaseDetails,
          ...(initialData.purchaseDetails || {}),
          taxPercentage: initialData.tax_percentage || 0,
        },
        cheque_no: initialData.cheque_no || "",
        bank_name: initialData.bank_name || "",
        issue_date: initialData.issue_date || "",
        id: initialData.id || null,
      };
    }

    const savedDraft = JSON.parse(localStorage.getItem(draftKey) || "null");
    if (savedDraft) {
      return {
        ...defaultState,
        ...savedDraft,
        invoice: {
          ...defaultState.invoice,
          ...savedDraft.invoice,
          date: formatDate(savedDraft.invoice?.date),
        },
        customer: {
          ...defaultState.customer,
          ...savedDraft.customer,
          id: savedDraft.customer?.id || null,
        },
        items: (savedDraft.items || []).map((item, idx) => {
          const qty = parseFloat(item.qty || 1);
          const salesPrice = parseFloat(item.salesPrice || 0);
          const buyingCost = parseFloat(item.buyingCost || 0);
          const unitPrice = parseFloat(item.unitPrice || 0);
          const mrp = parseFloat(item.mrp || unitPrice);
          const stock = parseFloat(item.stock || 0);

          return {
            ...item,
            id: item.id || Date.now() + idx,
            productId: item.productId || null,
            categoryId: item.categoryId || null,
            qty,
            unitPrice,
            mrp,
            salesPrice,
            buyingCost,
            stock,
            discountAmount: 0,
            specialDiscount: 0,
            total: 0,
            totalBuyingCost: 0,
            supplier: item.supplier || "",
            category: item.category || "",
            store_location: item.store_location || "",
          };
        }),
        purchaseDetails: {
          ...defaultState.purchaseDetails,
          ...(savedDraft.purchaseDetails || {}),
          taxPercentage: savedDraft.taxPercentage || 0,
        },
        cheque_no: savedDraft.cheque_no || "",
        bank_name: savedDraft.bank_name || "",
        issue_date: savedDraft.issue_date || "",
        id: savedDraft.id || null,
      };
    }

    return defaultState;
  });

  const [products, setProducts] = useState([]);
  const [customers, setCustomers] = useState([]);
  const [categories, setCategories] = useState([]);
  const [customersLoading, setCustomersLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [discountSchemes, setDiscountSchemes] = useState([]);

  // Clear form to default state
  const clearForm = () => {
    setFormData(getDefaultFormState());
    setErrors({});
    setNewItem({
      productId: null,
      qty: 1,
      unitPrice: 0,
      discountAmount: 0,
    });
  };

  // Override onCancel to clear form
  const handleCancel = () => {
    clearForm();
    if (onCancel) {
      onCancel();
    }
  };

  const newItemQtyRef = useRef(null);
  const newItemUnitPriceRef = useRef(null);
  const newItemDiscountAmountRef = useRef(null);
  const newItemProductSelectRef = useRef(null);
  const newItemFreeRef = useRef(null);
  const addItemButtonRef = useRef(null);
  const submitButtonRef = useRef(null);

  const [newItem, setNewItem] = useState({
    productId: null,
    qty: 1,
    free: 0,
    unitPrice: 0,
    discountAmount: 0,
  });

  const handleNewItemProductSelect = (selectedOption) => {
    const productId = selectedOption ? selectedOption.value : null;
    // When variantId is included in value (e.g. "productId-variantId"), split it
    let actualProductId = productId;
    let variantId = null;
    if (typeof productId === "string" && productId.includes("-")) {
      const parts = productId.split("-");
      actualProductId = parts[0];
      variantId = parts[1];
    }
    const product = products.find(
      (p) => p.product_id.toString() === actualProductId
    );

    if (!product || !product.variants || product.variants.length === 0) {
      // No variants, set newItem state only, do not add to formData.items yet
      setNewItem((prev) => ({
        ...prev,
        productId: actualProductId,
        unitPrice: product ? parseFloat(product.mrp) || 0 : 0,
        discountAmount: 0,
        description: product ? product.product_name : "",
        variantId: null,
      }));
      setErrors((prev) => ({ ...prev, newItemDescription: undefined }));
      return;
    }

    // If variants exist, set newItem state for selected variant or all variants (if variantId is set, only that variant)
    let variantsToAdd = product.variants;
    if (variantId) {
      variantsToAdd = product.variants.filter(
        (variant) => variant.product_variant_id.toString() === variantId
      );
    }

    // For simplicity, if multiple variants, do not add immediately, just set newItem to first variant
    const firstVariant = variantsToAdd[0];
    if (firstVariant) {
      setNewItem((prev) => ({
        ...prev,
        productId: product.product_id,
        variantId: firstVariant.product_variant_id,
        unitPrice: parseFloat(firstVariant.mrp) || 0, // Use MRP from variant, not sales_price
        discountAmount: 0,
        description: product.product_name,
      }));
      setErrors((prev) => ({ ...prev, newItemDescription: undefined }));
    }
  };

  const handleNewItemInputChange = (e, field) => {
    const value = e.target.value;
    setNewItem((prev) => ({
      ...prev,
      [field]:
        field === "qty" ||
        field === "free" ||
        field === "unitPrice" ||
        field === "discountAmount"
          ? value === ""
            ? ""
            : parseFloat(value) || 0
          : value,
    }));
    setErrors((prev) => ({
      ...prev,
      [`newItem${field.charAt(0).toUpperCase() + field.slice(1)}`]: undefined,
    }));
  };

  // Keyboard navigation handlers for new item form
  const handleNewItemQtyKeyDown = (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      newItemFreeRef.current?.focus();
      newItemFreeRef.current?.select();
    }
  };

  const handleNewItemFreeKeyDown = (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      newItemUnitPriceRef.current?.focus();
      newItemUnitPriceRef.current?.select();
    }
  };

  const handleNewItemUnitPriceKeyDown = (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      newItemDiscountAmountRef.current?.focus();
      newItemDiscountAmountRef.current?.select();
    }
  };

  const handleNewItemDiscountAmountKeyDown = (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      if (newItem.productId && newItem.qty > 0) {
        handleAddNewItem();
      }
    }
  };

  const handleAddNewItem = () => {
    const { productId, variantId, qty, unitPrice, discountAmount } = newItem;
    const newErrors = {};
    if (qty === "" || qty <= 0)
      newErrors.newItemQty = "Quantity must be positive";
    if (unitPrice === "" || unitPrice < 0)
      newErrors.newItemUnitPrice = "Unit price must be non-negative";
    if (discountAmount !== "" && discountAmount < 0)
      newErrors.newItemDiscountAmount = "Discount cannot be negative";
    if (Object.keys(newErrors).length > 0) {
      setErrors((prev) => ({ ...prev, ...newErrors }));
      return;
    }

    const product = products.find((p) => p.product_id === productId);

    // Find the specific variant if variantId is provided
    let selectedVariant = null;
    if (variantId && product && product.variants) {
      selectedVariant = product.variants.find(
        (variant) => variant.product_variant_id === variantId
      );
    }

    setFormData((prev) => {
      // Check if product with same variant already exists in items
      const existingIndex = prev.items.findIndex(
        (item) => item.productId === productId && item.variantId === variantId
      );

      if (existingIndex !== -1) {
        // Update existing item quantity and recalculate totals
        const updatedItems = [...prev.items];
        const existingItem = updatedItems[existingIndex];
        const newQty = existingItem.qty + qty;

        // Calculate new manual discountAmount proportionally
        const oldQty = existingItem.qty || 1;
        const oldDiscountAmount = existingItem.discountAmount || 0;
        let newDiscountAmount = 0;
        if (oldQty > 0) {
          newDiscountAmount = (oldDiscountAmount / oldQty) * newQty;
        }

        const updatedItem = {
          ...existingItem,
          qty: newQty,
          specialDiscount: 0,
          total: 0,
          discountAmount: newDiscountAmount,
        };

        // Recalculate discountAmount and totals for updated item
        const recalculatedItem = updateItemTotal(
          updatedItem,
          prev.invoice.date
        );

        updatedItems[existingIndex] = recalculatedItem;

        return {
          ...prev,
          items: updatedItems,
        };
      } else {
        // Add new item as usual
        // Fix: Set description to product_name or "N/A" if missing
        const description =
          product && product.product_name && product.product_name.trim() !== ""
            ? product.product_name
            : "N/A";

        // Use variant-specific pricing if variant is selected, otherwise use product-level pricing
        const itemMrp = selectedVariant
          ? parseFloat(selectedVariant.mrp) || 0
          : product
            ? parseFloat(product.mrp) || 0
            : unitPrice;
        const itemSalesPrice = selectedVariant
          ? parseFloat(selectedVariant.sales_price) || 0
          : product
            ? parseFloat(product.sales_price) || 0
            : 0;
        const itemBuyingCost = selectedVariant
          ? parseFloat(selectedVariant.buying_cost) || 0
          : product
            ? parseFloat(product.buying_cost) || 0
            : 0;
        const itemStock = selectedVariant
          ? parseFloat(selectedVariant.opening_stock_quantity) || 0
          : product
            ? parseFloat(product.opening_stock_quantity) || 0
            : 0;
        const itemStoreLocation = selectedVariant
          ? selectedVariant.store_location || ""
          : product
            ? product.store_location || ""
            : "";

        const newItemToAdd = {
          id: Date.now(),
          productId,
          variantId,
          qty,
          free: newItem.free || 0,
          unitPrice: itemMrp,
          discountAmount: discountAmount || 0,
          discountPercentage: "",
          specialDiscount: 0,
          total: 0,
          totalBuyingCost: qty * itemBuyingCost,
          description,
          salesPrice: itemSalesPrice,
          buyingCost: itemBuyingCost,
          categoryId: product ? product.category_id : null,
          mrp: itemMrp,
          stock: itemStock,
          supplier: product ? product.supplier || "" : "",
          category: product ? product.category || "" : "",
          store_location: itemStoreLocation,
        };

        // Recalculate totals for the new item
        const newItemWithTotals = updateItemTotal(
          newItemToAdd,
          prev.invoice.date
        );

        return {
          ...prev,
          items: [...prev.items, newItemWithTotals],
        };
      }
    });

    setNewItem({
      productId: null,
      variantId: null,
      qty: 1,
      unitPrice: 0,
      discountAmount: 0,
    });
    setErrors((prev) => {
      const updated = {
        ...prev,
        newItemDescription: undefined,
        newItemQty: undefined,
        newItemUnitPrice: undefined,
        newItemDiscountAmount: undefined,
      };
      return Object.fromEntries(Object.entries(updated).filter(([_, v]) => v !== undefined));
    });
    newItemProductSelectRef.current?.focus();
  };

  useEffect(() => {
    const fetchQuotations = async () => {
      try {
        const response = await axios.get("/api/quotations");
        const options = response.data.map((q) => ({
          value: q.id,
          label: q.quotation_no || `Quotation #${q.id}`,
        }));
        setQuotations(options);
      } catch (error) {
        toast.error("Failed to load quotations");
      }
    };

    const fetchData = async () => {
      try {
        setCustomersLoading(true);
        const today = format(new Date(), "yyyy-MM-dd");

        const endpoints = [
          {
            url: "/api/detailed-stock-reports",
            params: { toDate: today },
            name: "stock",
          },
          { url: "/api/products", name: "products" },
          { url: "/api/customers", name: "customers" },
          { url: "/api/categories", name: "categories" },
          { url: "/api/discount-schemes", name: "schemes" },
          { url: "/api/quotations", name: "quotations" },
        ];

        const responses = await Promise.all(
          endpoints.map((endpoint) =>
            axios
              .get(endpoint.url, { params: endpoint.params /*, timeout: undefined*/ }) // Remove timeout
              .then((res) => ({ data: res.data, name: endpoint.name }))
              .catch((err) => {
                console.error(`Error fetching ${endpoint.url}:`, err);
                toast.error(`Failed to load ${endpoint.name} data`);
                return { data: null, name: endpoint.name };
              })
          )
        );

        // Convert responses to an object for easier access
        const responseData = responses.reduce((acc, response) => {
          acc[response.name] = response.data;
          return acc;
        }, {});

        // Process products with stock and category_name
        if (responseData.products?.data) {
          const productsWithStock = responseData.products.data.map(
            (product) => {
              const stockItem =
                responseData.stock?.data?.find(
                  (stock) => stock.itemCode === product.item_code
                ) ||
                responseData.stock?.find(
                  (stock) => stock.itemCode === product.item_code
                );

              return {
                ...product,
                product_id: product.product_id,
                category_name: product.category || "Unknown",
                opening_stock_quantity: stockItem
                  ? parseFloat(stockItem.closingStock || 0)
                  : parseFloat(product.opening_stock_quantity || 0),
                buying_cost: parseFloat(product.buying_cost || 0),
                variants: product.variants || [], // Include variants from API response
              };
            }
          );
          setProducts(productsWithStock);
        }

        if (responseData.customers?.data) {
          setCustomers(responseData.customers.data);
        } else if (responseData.customers) {
          // Handle case where data might not be nested under .data
          setCustomers(responseData.customers);
        }

        if (responseData.categories) {
          setCategories(
            responseData.categories.data || responseData.categories
          );
        }

        if (responseData.quotations?.data || responseData.quotations) {
          setQuotations(
            responseData.quotations.data || responseData.quotations
          );
        }

        if (responseData.schemes?.data || responseData.schemes) {
          setDiscountSchemes(responseData.schemes.data || responseData.schemes);
        }
      } catch (error) {
        console.error("Error in main fetch:", error);
        toast.error("Failed to load some data. Check console for details.");
      } finally {
        setCustomersLoading(false);
      }
    };

    fetchData();
    fetchQuotations();
  }, []); // Add dependencies if needed

  // Recalculate discounts when discountSchemes or products change
  useEffect(() => {
    if (discountSchemes.length > 0 && products.length > 0) {
      setFormData((prev) => ({
        ...prev,
        items: prev.items.map((item) =>
          updateItemTotal(item, prev.invoice.date)
        ),
      }));
    }
  }, [discountSchemes, products]);

  // Recalculate all items totals including discountAmount when items or invoice date change
  useEffect(() => {
    setFormData((prev) => ({
      ...prev,
      items: prev.items.map((item) => updateItemTotal(item, prev.invoice.date)),
    }));
  }, [products, discountSchemes, formData.invoice.date]);

  useEffect(() => {
    itemRefs.current = formData.items.map((_, index) => ({
      description: { current: null },
      qty: { current: null },
      unitPrice: { current: null },
      discountAmount: { current: null },
      discountPercentage: { current: null },
    }));
  }, [formData.items.length]);

  useEffect(() => {
    quotationSelectRef.current?.focus();
  }, []);

  useEffect(() => {
    if (!isEditMode) {
      const { id, status, ...draftData } = formData;
      localStorage.setItem(draftKey, JSON.stringify(draftData));
    }
  }, [formData, isEditMode]);

  // Calculate special discount based on product or category
  const calculateSpecialDiscount = useCallback(
    (item, invoiceDate) => {
      if (!item.productId) {
        return { discount: 0, scheme: null, schemeType: null };
      }

      const baseProduct = products.find((p) => p.product_id === item.productId);
      if (!baseProduct) {
        console.warn(`Product not found for productId: ${item.productId}`);
        return { discount: 0, scheme: null, schemeType: null };
      }

      // If item has variantId, find the specific variant, otherwise use the base product
      let product;
      if (
        item.variantId &&
        baseProduct.variants &&
        baseProduct.variants.length > 0
      ) {
        const variant = baseProduct.variants.find(
          (v) => v.product_variant_id === item.variantId
        );
        if (variant) {
          // Merge base product with variant data
          product = {
            ...baseProduct,
            ...variant,
            variant_id: variant.product_variant_id,
            batch_number: variant.batch_number,
            expiry_date: variant.expiry_date,
            sales_price: variant.sales_price,
            mrp: variant.mrp,
          };
        } else {
          console.warn(`Variant not found for variantId: ${item.variantId}`);
          product = baseProduct;
        }
      } else {
        product = baseProduct;
      }

      const categoryName = product.category_name || "Unknown";
      if (categoryName === "Unknown") {
        console.warn(
          `No valid category_name for product: ${product.product_name} (category_id: ${product.category_id})`
        );
      }

      const qty = parseFloat(item.qty) || 1;
      const salesPrice = parseFloat(item.unitPrice) || 0;
      const totalAmount = qty * salesPrice;

      const applicableScheme = discountSchemes.find((scheme) => {
        if (
          !scheme.active ||
          !isDateWithinScheme(invoiceDate, scheme.start_date, scheme.end_date)
        ) {
          return false;
        }
        const target = scheme.target?.trim().toLowerCase();

        // Create display name for batch-wise matching
        // Use item data first (from cart), then fall back to product data (from items array)
        const batchNumber = item.batch_number || product.batch_number;
        const expiryDate = item.expiry_date || product.expiry_date;

        const batchInfo = batchNumber ? ` (Batch: ${batchNumber})` : "";
        const expiryInfo = expiryDate
          ? ` (Exp: ${expiryDate.split("T")[0]})`
          : "";
        const displayName = `${product.product_name}${batchInfo}${expiryInfo}`;

        const productMatch =
          scheme.applies_to === "product" &&
          (target === (product.description?.trim().toLowerCase() || "") ||
            target === (product.product_name?.trim().toLowerCase() || "") ||
            target === displayName.trim().toLowerCase());
        const categoryMatch =
          scheme.applies_to === "category" &&
          categoryName &&
          target === categoryName?.trim().toLowerCase();
        return productMatch || categoryMatch;
      });

      if (!applicableScheme) {
        return { discount: 0, scheme: null, schemeType: null };
      }

      let discount = 0;
      if (applicableScheme.type === "percentage") {
        discount = (totalAmount * parseFloat(applicableScheme.value)) / 100;
      } else if (applicableScheme.type === "amount") {
        discount = parseFloat(applicableScheme.value) * qty;
      }

      const schemeType =
        applicableScheme.applies_to === "product" ? "product" : "category";
      return {
        discount: discount >= 0 ? discount : 0,
        scheme: applicableScheme,
        schemeType,
      };
    },
    [products, discountSchemes]
  );

  // Update item totals including special discount
  const updateItemTotal = useCallback(
    (item, invoiceDate) => {
      const qty = parseFloat(item.qty) || 0;
      const unitPrice = parseFloat(item.unitPrice) || 0;
      const mrp = parseFloat(item.mrp) || 0;
      const buyingCost = parseFloat(item.buyingCost) || 0;
      const discountPercentage = parseFloat(item.discountPercentage) || 0;

      // Calculate discount amount as (mrp - selling price) * qty + special discount amount
      const {
        discount: specialDiscount,
        scheme,
        schemeType,
      } = calculateSpecialDiscount(item, invoiceDate);

      const baseDiscount = (mrp - item.salesPrice) * qty;
      const baseDiscountNonNegative = baseDiscount >= 0 ? baseDiscount : 0;

      // Use manual discountAmount if set and different from calculated baseDiscount + specialDiscount
      let discountAmount = baseDiscountNonNegative + specialDiscount;
      if (
        typeof item.discountAmount === "number" &&
        item.discountAmount !== 0 &&
        item.discountAmount !== discountAmount
      ) {
        discountAmount = item.discountAmount;
      }

      // Add debug logs for discount calculation
      if (item.variantId || specialDiscount > 0) {
        const baseProduct = products.find(
          (p) => p.product_id === item.productId
        );
        console.log(`[SalesInvoice] Special discount debug:`, {
          item_id: item.id,
          product_name: baseProduct?.product_name || "Unknown",
          variant_id: item.variantId,
          base_discount: baseDiscountNonNegative.toFixed(2),
          special_discount: specialDiscount.toFixed(2),
          total_discount: discountAmount.toFixed(2),
          scheme: scheme?.target || "none",
        });
      }

      const totalDiscount = discountAmount;

      // Calculate total as qty * mrp minus total discount amount
      let total = qty * mrp - totalDiscount;

      const profit =
        qty * (parseFloat(item.salesPrice) || 0) - qty * buyingCost;

      return {
        ...item,
        discountAmount: totalDiscount >= 0 ? totalDiscount : 0,
        discountPercentage,
        specialDiscount,
        totalDiscount,
        discountScheme: scheme,
        discountSchemeType: schemeType,
        total: total >= 0 ? total : 0,
        totalBuyingCost: qty * buyingCost,
        mrp,
        profit: profit >= 0 ? profit : 0,
        unitPrice: unitPrice || 0,
        // totalBuyingCost: qty * buyingCost || 0,
        baseDiscount: baseDiscountNonNegative,
      };
    },
    [calculateSpecialDiscount]
  );
  const handlePreparedByChange = (e) => {
    setFooterDetails((prev) => ({ ...prev, preparedBy: e.target.value }));
  };

  const handleApprovedByChange = (selectedOption) => {
    console.log("Approved By selected:", selectedOption); // Debug log
    // Allow clearing approvedBy to null
    setFooterDetails((prev) => ({
      ...prev,
      approvedBy: selectedOption,
    }));
    // Remove incorrect ref overwrite
    // approvedByRef.current = selectedOption;
  };

  const handleInputChange = (e, section, field, index = null) => {
    const { name, value } = e.target;
    const targetName = name || field;
    let processedValue = value;

    setFormData((prev) => {
      const newData = { ...prev };
      if (index !== null && section === "items") {
        const newItems = [...newData.items];
        if (
          targetName === "qty" ||
          targetName === "unitPrice" ||
          targetName === "discountAmount" ||
          targetName === "salesPrice" ||
          targetName === "mrp"
        ) {
          processedValue = value === "" ? "" : parseFloat(value) || 0;
          newItems[index] = {
            ...newItems[index],
            [targetName]: processedValue,
          };
          // Sync salesPrice with unitPrice when unitPrice changes
          if (targetName === "unitPrice") {
            newItems[index].salesPrice = processedValue;
          }
          // Update discountAmount to (mrp - salesPrice) * qty
          const qty = parseFloat(newItems[index].qty) || 0;
          const mrp = parseFloat(newItems[index].mrp) || 0;
          const salesPrice = parseFloat(newItems[index].salesPrice) || 0;
          const discountAmount = (mrp - salesPrice) * qty;
          newItems[index].discountAmount =
            discountAmount >= 0 ? discountAmount : 0;

          newItems[index] = updateItemTotal(
            newItems[index],
            newData.invoice.date
          );
        } else {
          newItems[index] = {
            ...newItems[index],
            [targetName]: processedValue,
          };
        }
        newData.items = newItems;
      } else {
        if (
          section === "purchaseDetails" &&
          (targetName === "amount" || targetName === "taxPercentage")
        ) {
          processedValue = value === "" ? "" : parseFloat(value) || 0;
        }
        newData[section] = {
          ...newData[section],
          [targetName]: processedValue,
        };
        if (section === "invoice" && targetName === "date") {
          newData.items = newData.items.map((item) =>
            updateItemTotal(item, processedValue)
          );
        }
      }
      return newData;
    });

    setErrors((prev) => {
      const updated = {
        ...prev,
        [`${
          section === "items"
            ? `item${
                targetName.charAt(0).toUpperCase() + targetName.slice(1)
              }${index}`
            : `${section}${
                targetName.charAt(0).toUpperCase() + targetName.slice(1)
              }`
        }`]: undefined,
        items: undefined,
        purchaseAmount: undefined,
      };
      return Object.fromEntries(Object.entries(updated).filter(([_, v]) => v !== undefined));
    });
  };

  // const handleQuotationChange = async (selectedOption) => {
  //   setSelectedQuotation(selectedOption);
  //   if (selectedOption) {
  //     try {
  //       const response = await axios.get(
  //         `/api/quotations/${selectedOption.value}`
  //       );
  //       const quotationData = response.data;

  //       setFormData((prev) => ({
  //         ...prev,
  //         invoice: {
  //           ...prev.invoice,
  //           no: quotationData.quotation_no || prev.invoice.no,
  //           date: quotationData.date || prev.invoice.date,
  //           time: quotationData.time || prev.invoice.time,
  //         },
  //         customer: {
  //           id: quotationData.customer_id || null,
  //           name: quotationData.customer_name || "",
  //           address: quotationData.customer_address || "",
  //           phone: quotationData.customer_phone || "",
  //           email: quotationData.customer_email || "",
  //         },
  //         items: quotationData.items.map((item) => ({
  //           id: Date.now() + Math.random(),
  //           productId: item.product_id,
  //           description: item.description,
  //           qty: item.qty,
  //           unitPrice: item.unit_price,
  //           salesPrice: item.sales_price,
  //           discountAmount: item.discount_amount,
  //           discountPercentage: item.discount_percentage,
  //           specialDiscount: item.special_discount,
  //           total: item.total,
  //           totalBuyingCost: item.total_buying_cost,
  //           supplier: item.supplier,
  //           category: item.category,
  //           store_location: item.store_location,
  //           mrp: item.mrp,
  //           stock: item.stock || 0,
  //         })),
  //       }));
  //     } catch (error) {
  //       toast.error("Failed to load quotation details");
  //     }
  //   }
  // };

  const supplierOptions = useMemo(() => {
    const unique = new Set();
    products.forEach((p) => {
      if (p.supplier && p.supplier.trim() !== "") unique.add(p.supplier.trim());
    });
    return [
      { value: null, label: "All Suppliers" },
      ...Array.from(unique).map((s) => ({ value: s, label: s })),
    ];
  }, [products]);

  const handleQuotationChange = async (selectedOption) => {
    setSelectedQuotation(selectedOption);
    if (selectedOption) {
      try {
        const response = await axios.get(
          `/api/quotations/${selectedOption.value}`
        );
        const quotationData = response.data;

        console.log("Quotation data received:", quotationData);

        // Defensive mapping of customer fields, checking nested customer object if present
        const customerInfo = quotationData.customer || {
          id: quotationData.customer_id || null,
          name: quotationData.customer_name || "",
          address: quotationData.customer_address || "",
          phone: quotationData.customer_phone || "",
          email: quotationData.customer_email || "",
        };

        // Ensure customer name is not empty string or null
        const customerName =
          customerInfo.name && customerInfo.name.trim() !== ""
            ? customerInfo.name
            : "Unknown Customer";

        // First, find the matching customer in the customerOptions to ensure proper selection
        const matchingCustomer = customerOptions.find(
          (option) =>
            option.value === customerInfo.id ||
            option.value?.toString() === customerInfo.id?.toString() ||
            option.label === customerInfo.name
        );

        setFormData((prev) => ({
          ...prev,
          customer: {
            id: matchingCustomer
              ? matchingCustomer.value
              : customerInfo.id || null,
            name: matchingCustomer ? matchingCustomer.label : customerInfo.name,
            address: customerInfo.address || "",
            phone: customerInfo.phone || "",
            email: customerInfo.email || "",
          },
          items: quotationData.items.map((item) => ({
            id: Date.now() + Math.random(),
            productId: item.product_id,
            description: item.description,
            qty: item.qty,
            unitPrice: item.mrp,
            salesPrice:
              item.sales_price !== undefined
                ? item.sales_price
                : item.selling_price,
            discountAmount: item.discount_amount,
            discountPercentage: item.discount_percentage,
            specialDiscount: item.special_discount,
            total: item.total,
            totalBuyingCost: item.total_buying_cost,
            supplier: item.supplier,
            category: item.category,
            store_location: item.store_location,
            mrp: item.mrp,
            stock: item.stock || 0,
          })),
        }));

        // Clear any customer-related errors since we're auto-selecting a valid customer
        setErrors((prev) => {
          const updated = {
            ...prev,
            customerName: undefined,
            customerEmail: undefined,
          };
          return Object.fromEntries(Object.entries(updated).filter(([_, v]) => v !== undefined));
        });

        // Debug log to verify customer data is properly set
        console.log("Quotation customer data loaded:", {
          originalCustomerInfo: customerInfo,
          matchingCustomer: matchingCustomer,
          finalCustomerData: {
            id: matchingCustomer
              ? matchingCustomer.value
              : customerInfo.id || null,
            name: matchingCustomer ? matchingCustomer.label : customerInfo.name,
            address: customerInfo.address || "",
            phone: customerInfo.phone || "",
            email: customerInfo.email || "",
          },
        });
        setFooterDetails((prev) => ({
          ...prev,
          approvedBy: quotationData.approved_by || "",
        }));

        // Move focus to invoice number field after selecting quotation
        setTimeout(() => {
          invoiceNoRef.current?.focus();
          invoiceNoRef.current?.select?.();
        }, 100);
      } catch (error) {
        toast.error("Failed to load quotation details");
      }
    } else {
      // If quotation is cleared, also auto-navigate to next field
      setTimeout(() => {
        invoiceNoRef.current?.focus();
        invoiceNoRef.current?.select?.();
      }, 100);
    }
  };

  useEffect(() => {
    if (!isEditMode) {
      setFormData({
        invoice: {
          no: "",
          date: format(new Date(), "yyyy-MM-dd"),
          time: format(new Date(), "HH:mm"),
        },
        customer: { id: null, name: "", address: "", phone: "", email: "" },
        items: [],
        purchaseDetails: { method: "cash", amount: 0, taxPercentage: 0 },
        cheque_no: "",
        bank_name: "",
        issue_date: "",
        status: "unpaid",
        id: null,
      });
      setSelectedQuotation(null);
      setErrors({});
    }
  }, [isEditMode]);

  const handleCustomerSelect = async (selectedOption) => {
    if (!selectedOption) {
      setFormData((prev) => ({
        ...prev,
        customer: { id: null, name: "", address: "", phone: "", email: "" },
      }));
      setCustomerLoyaltyPoints(null);
      setLoyaltyError(null);
      setErrors((prev) => {
        const updated = {
          ...prev,
          customerName: undefined,
          customerEmail: undefined,
        };
        return Object.fromEntries(Object.entries(updated).filter(([_, v]) => v !== undefined));
      });
      setRedeemPoints(0); // Reset redeem amount when customer changes
      return;
    }
    const customer = customers.find(
      (c) => c.id === selectedOption.value || c.customer_id === selectedOption.value
    );
    setFormData((prev) => ({
      ...prev,
      customer: {
        id: customer ? (customer.id ?? customer.customer_id) : null,
        name: customer ? customer.customer_name || customer.name || "" : "",
        address: customer ? customer.address || "" : "",
        phone: customer ? customer.phone || "" : "",
        email: customer ? customer.email || "" : "",
      },
    }));
    setErrors((prev) => {
      const updated = {
        ...prev,
        customerName: undefined,
        customerEmail: undefined,
      };
      return Object.fromEntries(Object.entries(updated).filter(([_, v]) => v !== undefined));
    });
    setTimeout(() => customerAddressRef.current?.focus(), 0);

    // Fetch loyalty points for the selected customer
    if (customer && (customer.id || customer.customer_id)) {
      setLoyaltyLoading(true);
      setLoyaltyError(null);
      setCustomerLoyaltyPoints(null);
      try {
        // Replace with your actual API endpoint if different
        const res = await axios.get(`/api/loyalty-points`, {
          params: { customer_id: customer.id || customer.customer_id },
        });
        // The API may return a list or a single object; adjust as needed
        let points = null;
        if (Array.isArray(res.data.data)) {
          const found = res.data.data.find(
            (c) => c.id === customer.id || c.id === customer.customer_id
          );
          points = found ? found.points_balance : null;
        } else if (res.data.data && typeof res.data.data === 'object') {
          points = res.data.data.points_balance;
        }
        setCustomerLoyaltyPoints(points ?? 0);
      } catch (err) {
        setLoyaltyError("Failed to fetch loyalty points");
        setCustomerLoyaltyPoints(null);
      } finally {
        setLoyaltyLoading(false);
      }
    } else {
      setCustomerLoyaltyPoints(null);
    }
  };

  const handleProductSelect = async (selectedOption, index) => {
    const productId = selectedOption ? selectedOption.value : null;
    // When variantId is included in value (e.g. "productId-variantId"), split it
    let actualProductId = productId;
    let variantId = null;
    if (typeof productId === "string" && productId.includes("-")) {
      const parts = productId.split("-");
      actualProductId = parts[0];
      variantId = parts[1];
    }
    const product = products.find(
      (p) => p.product_id.toString() === actualProductId
    );

    if (!product || !product.variants || product.variants.length === 0) {
      // No variants, check if this product already exists in other items
      setFormData((prev) => {
        const newItems = [...prev.items];
        if (!newItems[index]) return prev;

        const currentItem = newItems[index];
        const currentQty = parseFloat(currentItem.qty) || 1;

        // Check if the same product (without variant) already exists in other items
        console.log("Checking for existing product:", {
          actualProductId,
          actualProductIdType: typeof actualProductId,
          currentIndex: index,
          allItems: newItems.map((item, idx) => ({
            index: idx,
            productId: item.productId,
            productIdType: typeof item.productId,
            variantId: item.variantId,
            description: item.description,
          })),
        });

        const existingIndex = newItems.findIndex((item, idx) => {
          const isSameIndex = idx !== index;
          const isSameProduct =
            item.productId?.toString() === actualProductId?.toString();
          const hasNoVariant =
            !item.variantId ||
            item.variantId === null ||
            item.variantId === undefined;

          console.log(`Checking item ${idx}:`, {
            isSameIndex,
            isSameProduct,
            hasNoVariant,
            itemProductId: item.productId,
            actualProductId,
            result: isSameIndex && isSameProduct && hasNoVariant,
          });

          return isSameIndex && isSameProduct && hasNoVariant;
        });

        console.log("Found existing index:", existingIndex);

        if (existingIndex !== -1) {
          // Merge with existing item
          const existingItem = newItems[existingIndex];
          const newQty = existingItem.qty + currentQty;

          // Calculate new manual discountAmount
          const oldDiscountAmount = existingItem.discountAmount || 0;
          const currentDiscountAmount = currentItem.discountAmount || 0;
          let newDiscountAmount = oldDiscountAmount + currentDiscountAmount;

          const updatedItem = {
            ...existingItem,
            qty: newQty,
            discountAmount: newDiscountAmount,
            specialDiscount: 0,
            total: 0,
          };

          // Recalculate totals for updated item
          const recalculatedItem = updateItemTotal(
            updatedItem,
            prev.invoice.date
          );
          newItems[existingIndex] = recalculatedItem;

          // Remove the current item since it's been merged
          newItems.splice(index, 1);

          return { ...prev, items: newItems };
        } else {
          // Update current item with new product details
          const mrp = product ? parseFloat(product.mrp) || 0 : 0;
          const unitPriceValue = mrp;
          const buyingCost = product ? parseFloat(product.buying_cost) || 0 : 0;
          const stock = product
            ? parseFloat(product.opening_stock_quantity) || 0
            : 0;

          // Fix: Ensure description is set properly, fallback to product_name or "N/A"
          const description =
            product &&
            product.product_name &&
            product.product_name.trim() !== ""
              ? product.product_name
              : "N/A";

          // Preserve the original item ID to ensure backend updates existing item
          const originalId = newItems[index].id;

          newItems[index] = {
            ...newItems[index],
            id: originalId, // Explicitly preserve the original ID
            productId: product ? product.product_id : null,
            variantId: null,
            categoryId: product ? product.category_id : null,
            description,
            unitPrice: unitPriceValue,
            mrp,
            salesPrice:
              newItems[index].salesPrice !== undefined &&
              newItems[index].salesPrice !== null
                ? newItems[index].salesPrice
                : product
                  ? parseFloat(product.sales_price) || 0
                  : 0,
            buyingCost,
            stock,
            discountAmount: newItems[index].discountAmount || 0, // Preserve existing discount
            discountPercentage: newItems[index].discountPercentage || "",
            totalBuyingCost: currentQty * buyingCost,
            supplier: product ? product.supplier || "" : "",
            category: product ? product.category || "" : "",
            store_location: itemStoreLocation,
          };

          newItems[index] = updateItemTotal(newItems[index], prev.invoice.date);
          return { ...prev, items: newItems };
        }
      });
      setErrors((prev) => {
        const updated = {
          ...prev,
          [`itemDescription${index}`]: undefined,
        };
        return Object.fromEntries(Object.entries(updated).filter(([_, v]) => v !== undefined));
      });
      setTimeout(() => itemRefs.current[index]?.qty?.current?.focus(), 0);
      return;
    }

    // If variants exist, check if the selected variant already exists and merge if so
    setFormData((prev) => {
      const newItems = [...prev.items];
      const originalItem = newItems[index] || {};
      const currentQty = parseFloat(originalItem.qty) || 1;

      let variantsToAdd = product.variants;
      if (variantId) {
        variantsToAdd = product.variants.filter(
          (variant) => variant.product_variant_id.toString() === variantId
        );
      }

      // If only one specific variant is selected, check if it already exists
      if (variantId && variantsToAdd.length === 1) {
        const selectedVariant = variantsToAdd[0];

        // Check if the same product+variant combination already exists in other items
        const existingIndex = newItems.findIndex(
          (item, idx) =>
            idx !== index &&
            item.productId?.toString() === product.product_id.toString() &&
            item.variantId?.toString() ===
              selectedVariant.product_variant_id.toString()
        );

        if (existingIndex !== -1) {
          // Merge with existing item
          const existingItem = newItems[existingIndex];
          const newQty = existingItem.qty + currentQty;

          // Calculate new manual discountAmount
          const oldDiscountAmount = existingItem.discountAmount || 0;
          const currentDiscountAmount = originalItem.discountAmount || 0;
          let newDiscountAmount = oldDiscountAmount + currentDiscountAmount;

          const updatedItem = {
            ...existingItem,
            qty: newQty,
            discountAmount: newDiscountAmount,
            specialDiscount: 0,
            total: 0,
          };

          // Recalculate totals for updated item
          const recalculatedItem = updateItemTotal(
            updatedItem,
            prev.invoice.date
          );
          newItems[existingIndex] = recalculatedItem;

          // Remove the current item since it's been merged
          newItems.splice(index, 1);

          return { ...prev, items: newItems };
        } else {
          // Update current item with selected variant details
          const selectedVariant = variantsToAdd[0];
          const originalId = originalItem.id;

          newItems[index] = {
            ...originalItem,
            id: originalId,
            productId: product.product_id,
            variantId: selectedVariant.product_variant_id,
            description: product.product_name,
            unitPrice: parseFloat(selectedVariant.mrp) || 0,
            salesPrice: parseFloat(selectedVariant.sales_price) || 0,
            buyingCost: parseFloat(selectedVariant.buying_cost) || 0,
            categoryId: product.category_id || null,
            mrp: parseFloat(selectedVariant.mrp) || 0,
            stock: parseFloat(selectedVariant.opening_stock_quantity) || 0,
            supplier: product.supplier || "",
            category: product.category || "",
            store_location: selectedVariant.store_location || "",
            totalBuyingCost:
              currentQty * (parseFloat(selectedVariant.buying_cost) || 0),
            total: 0,
          };

          newItems[index] = updateItemTotal(newItems[index], prev.invoice.date);
          return { ...prev, items: newItems };
        }
      } else {
        // Multiple variants or no specific variant selected - replace with all variants
        // Remove the item at index
        newItems.splice(index, 1);

        const variantItems = variantsToAdd.map((variant, variantIndex) => {
          return {
            // For the first variant, preserve the original item ID if it exists
            // For additional variants, create new IDs
            id:
              variantIndex === 0 && originalItem.id
                ? originalItem.id
                : Date.now() + Math.random(),
            productId: product.product_id,
            variantId: variant.product_variant_id,
            batch_number: variant.batch_number || null, // Batch tracking
            expiry_date: variant.expiry_date || null, // Batch tracking
            qty: variantIndex === 0 ? originalItem.qty || 1 : 1,
            free: variantIndex === 0 ? originalItem.free || 0 : 0,
            unitPrice: parseFloat(variant.mrp) || 0,
            discountAmount:
              variantIndex === 0 ? originalItem.discountAmount || 0 : 0,
            discountPercentage:
              variantIndex === 0 ? originalItem.discountPercentage || "" : "",
            specialDiscount:
              variantIndex === 0 ? originalItem.specialDiscount || 0 : 0,
            total: 0,
            totalBuyingCost: 0,
            // Show only product name in item list description, no batch number
            description: product.product_name,
            salesPrice: parseFloat(variant.sales_price) || 0,
            buyingCost: parseFloat(variant.buying_cost) || 0,
            categoryId: product.category_id || null,
            mrp: parseFloat(variant.mrp) || 0,
            stock: parseFloat(variant.opening_stock_quantity) || 0,
            supplier: product.supplier || "",
            category: product.category || "",
            store_location: variant.store_location || "",
          };
        });
        newItems.splice(index, 0, ...variantItems);
        return { ...prev, items: newItems };
      }
    });
    setErrors((prev) => {
      const updated = {
        ...prev,
        [`itemDescription${index}`]: undefined,
      };
      return Object.fromEntries(Object.entries(updated).filter(([_, v]) => v !== undefined));
    });
    setTimeout(() => itemRefs.current[index]?.qty?.current?.focus(), 0);
  };

  const addItem = () => {
    setFormData((prev) => ({
      ...prev,
      items: [
        ...prev.items,
        {
          id: Date.now(),
          description: "",
          qty: 1,
          unitPrice: "",
          salesPrice: 0,
          discountAmount: 0,
          discountPercentage: "",
          specialDiscount: 0,
          total: 0,
          totalBuyingCost: 0,
          productId: null,
          buyingCost: 0,
          categoryId: null,
          mrp: 0,
          stock: 0,
        },
      ],
    }));
    setTimeout(
      () =>
        itemRefs.current[formData.items.length]?.description?.current?.focus(),
      0
    );
  };

  const removeItem = (index) => {
    setFormData((prev) => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index),
    }));
  };

  const calculateSubtotal = useCallback(() => {
    return formData.items.reduce((sum, item) => {
      const qty = parseFloat(item.qty) || 0;
      const mrp = parseFloat(item.mrp) || 0;
      return sum + qty * mrp;
    }, 0);
  }, [formData.items]);

  const calculateTotalDiscount = useCallback(() => {
    return formData.items.reduce((sum, item) => {
      const discountAmount = parseFloat(item.discountAmount) || 0;
      // specialDiscount is already included in discountAmount, so do not add again
      return sum + discountAmount;
    }, 0);
  }, [formData.items]);

  const calculateTax = useCallback(
    (subtotal) => {
      const taxPercentage =
        parseFloat(formData.purchaseDetails.taxPercentage) || 0;
      return subtotal * (taxPercentage / 100);
    },
    [formData.purchaseDetails.taxPercentage]
  );

  const calculateTotal = useCallback(() => {
    const subtotal = calculateSubtotal();
    return subtotal + calculateTax(subtotal);
  }, [calculateSubtotal, calculateTax]);

  const calculateBalance = useCallback(() => {
    const paidAmount = parseFloat(formData.purchaseDetails.amount) || 0;
    const subtotal = calculateSubtotal();
    const tax = calculateTax(subtotal);
    const totalDiscount = calculateTotalDiscount();
    const grandTotal = subtotal + tax - totalDiscount;
    // For all methods, balance due means (paid - grand total)
    return paidAmount - grandTotal;
  }, [
    calculateSubtotal,
    calculateTax,
    calculateTotalDiscount,
    formData.purchaseDetails.amount,
    formData.purchaseDetails.method,
  ]);

  // Update status based on payment amount and total
  useEffect(() => {
    const amountPaid = parseFloat(formData.purchaseDetails.amount) || 0;
    const subtotal = calculateSubtotal();
    const tax = calculateTax(subtotal);
    const totalDiscount = calculateTotalDiscount();
    const grandTotal = subtotal + tax - totalDiscount;

    // New logic: always set status based on paid amount vs grand total
    if (amountPaid === 0) {
      if (formData.status !== "pending") {
        setFormData((prev) => ({ ...prev, status: "pending" }));
      }
    } else if (amountPaid < grandTotal) {
      if (formData.status !== "partial") {
        setFormData((prev) => ({ ...prev, status: "partial" }));
      }
    } else if (amountPaid >= grandTotal) {
      if (formData.status !== "paid") {
        setFormData((prev) => ({ ...prev, status: "paid" }));
      }
    }
  }, [formData.purchaseDetails.amount, calculateSubtotal, calculateTax, calculateTotalDiscount, formData.status]);

  const validateForm = (approvedByValue) => {
    console.log('validateForm - formData:', formData);
    const newErrors = {};
    const { invoice, customer, items, purchaseDetails } = formData;
    const total = calculateTotal();
    const amountPaid = parseFloat(purchaseDetails.amount) || 0;
    const totalPaid = (parseFloat(formData.purchaseDetails.amount) || 0) + (parseFloat(redeemPoints) || 0);

    const approvedByToCheck =
      approvedByValue !== undefined
        ? approvedByValue
        : footerDetails.approvedBy;
    console.log("validateForm - approvedBy:", approvedByToCheck);

    let approvedByName = "";
    if (approvedByToCheck) {
      if (typeof approvedByToCheck === "string") {
        approvedByName = approvedByToCheck.trim();
      } else if (
        typeof approvedByToCheck === "object" &&
        approvedByToCheck.label
      ) {
        approvedByName = approvedByToCheck.label.trim();
      }
    }

    if (!invoice.date) newErrors.invoiceDate = "Invoice date is required";
    if (!invoice.time || !/^\d{2}:\d{2}$/.test(invoice.time))
      newErrors.invoiceTime = "Invalid time format (HH:MM)";
    // Removed required validation for customer name as per user request
    // if (formData.purchaseDetails.method !== "credit" && !customer.name?.trim())
    //   newErrors.customerName = "Customer name is required";
    if (customer.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(customer.email))
      newErrors.customerEmail = "Invalid email address";
    if (!items.length) newErrors.items = "At least one item is required";
    if (
      purchaseDetails.taxPercentage !== "" &&
      parseFloat(purchaseDetails.taxPercentage) < 0
    ) {
      newErrors.purchaseTaxPercentage = "Tax percentage cannot be negative";
    }
    if (purchaseDetails.amount !== "" && amountPaid < 0)
      newErrors.purchaseAmount = "Amount paid cannot be negative";

    // Removed required validation for Approved By
    // if (!approvedByName) {
    //   newErrors.approvedBy = "Approved By is required";
    // }

    // Validate amount paid based on payment method
    const fullPaymentMethods = [
      "cash",
      "card",
      "online",
      "cheque",
      "bank_transfer",
    ];
    if (purchaseDetails.method === "cheque") {
      const subtotal = calculateSubtotal();
      const tax = calculateTax(subtotal);
      const totalDiscount = calculateTotalDiscount();
      const grandTotal = subtotal + tax - totalDiscount;
      if (totalPaid > grandTotal) {
        newErrors.purchaseAmount = `Total paid must not be greater than ${formatCurrency(grandTotal)} for cheque payments`;
      }
      // Cheque fields required
      if (!formData.cheque_no) newErrors.cheque_no = "Cheque number is required";
      if (!formData.bank_name) newErrors.bank_name = "Bank name is required";
      if (!formData.issue_date) newErrors.issue_date = "Issue date is required";
    } else if (
      fullPaymentMethods.includes(purchaseDetails.method) &&
      purchaseDetails.amount !== ""
    ) {
      const subtotal = calculateSubtotal();
      const tax = calculateTax(subtotal);
      const totalDiscount = calculateTotalDiscount();
      const grandTotal = subtotal + tax - totalDiscount;
      if (totalPaid < grandTotal) {
        newErrors.purchaseAmount = `Total paid must be at least ${formatCurrency(grandTotal)} for ${purchaseDetails.method} payments`;
      }
    }
    if (purchaseDetails.method === "credit" && purchaseDetails.amount !== "") {
      const subtotal = calculateSubtotal();
      const tax = calculateTax(subtotal);
      const totalDiscount = calculateTotalDiscount();
      const grandTotal = subtotal + tax - totalDiscount;
      if (totalPaid >= grandTotal) {
        newErrors.purchaseAmount = `Total paid must be less than ${formatCurrency(grandTotal)} for credit payments`;
      }
    }

    items.forEach((item, idx) => {
      if (item.qty === "" || parseFloat(item.qty) <= 0)
        newErrors[`itemQty${idx}`] = "Quantity must be positive";
      if (item.unitPrice === "" || parseFloat(item.unitPrice) < 0)
        newErrors[`itemUnitPrice${idx}`] = "Unit price must be non-negative";
      if (item.discountAmount !== "" && parseFloat(item.discountAmount) < 0)
        newErrors[`itemDiscountAmount${idx}`] =
          "Discount amount cannot be negative";
      if (
        item.discountPercentage !== "" &&
        (parseFloat(item.discountPercentage) < 0 ||
          parseFloat(item.discountPercentage) > 100)
      ) {
        newErrors[`itemDiscountPercentage${idx}`] =
          "Discount percentage must be between 0 and 100";
      }
      if (item.specialDiscount < 0)
        newErrors[`itemSpecialDiscount${idx}`] =
          "Special discount cannot be negative"; 
      // const product = products.find((p) => p.product_id === item.productId);
      // if (
      //   product &&
      //   parseFloat(item.qty) > parseFloat(product.opening_stock_quantity)
      // ) {
      //   newErrors[`itemQty${idx}`] =
      //     `Quantity exceeds available stock (${product.opening_stock_quantity})`;
      // }
    });

    // Filter out undefined values before setting errors
    const filteredErrors = Object.fromEntries(
      Object.entries(newErrors).filter(([_, v]) => v !== undefined)
    );
    console.log('validateForm - newErrors:', newErrors, 'filteredErrors:', filteredErrors);
    setErrors(filteredErrors);
    return Object.keys(filteredErrors).length === 0;
  };

  const handleSubmit = useCallback(
    async (e) => {
      e.preventDefault();
      // Debug log to check customer data before validation
      console.log("Form submission - Customer data:", {
        customerId: formData.customer.id,
        customerName: formData.customer.name,
        customerAddress: formData.customer.address,
        customerPhone: formData.customer.phone,
        customerEmail: formData.customer.email,
        fullFormData: formData,
      });

      if (!validateForm(footerDetails.approvedBy)) {
        toast.warn("Please fix validation errors.");
        console.log("Validation failed - Current errors:", errors);
        return;
      }

      // Generate invoice number if empty
      let invoiceNo = formData.invoice.no;
      if (!invoiceNo || invoiceNo.trim() === "") {
        // Use localStorage to keep track of last invoice number index
        const lastIndexStr = localStorage.getItem("lastInvoiceIndex");
        let lastIndex = lastIndexStr ? parseInt(lastIndexStr, 10) : 0;
        lastIndex += 1;
        localStorage.setItem("lastInvoiceIndex", lastIndex.toString());
        // Format invoice number as INV-001, INV-002, etc.
        invoiceNo = `INV-${lastIndex.toString().padStart(3, "0")}`;
        setFormData((prev) => ({
          ...prev,
          invoice: {
            ...prev.invoice,
            no: invoiceNo,
          },
        }));
        console.log("Generated invoice number:", invoiceNo);
      } else {
        console.log("Using existing invoice number:", invoiceNo);
      }

      // Additional validation for payload fields before sending
      if (!invoiceNo) {
        toast.error("Invoice number is required.");
        return;
      }
      if (!formData.invoice.date) {
        toast.error("Invoice date is required.");
        return;
      }
      if (!formData.invoice.time) {
        toast.error("Invoice time is required.");
        return;
      }
      if (!formData.items || formData.items.length === 0) {
        toast.error("At least one invoice item is required.");
        return;
      }
      for (const item of formData.items) {
        if (!item.productId) {
          toast.error("All items must have a valid product selected.");
          return;
        }
        if (item.qty <= 0) {
          toast.error("Item quantity must be greater than zero.");
          return;
        }
        if (item.unitPrice < 0) {
          toast.error("Item unit price cannot be negative.");
          return;
        }
      }

      setLoading(true);
      const payload = {
        invoice: {
          no: formData.invoice.no, // auto-generated
          date: formData.invoice.date, // manually selected
          time: formData.invoice.time, // manually selected
        },
        customer: {
          id: formData.customer.id || null,
          name: formData.customer.name,
          address: formData.customer.address || null,
          phone: formData.customer.phone || null,
          email: formData.customer.email || null,
        },
        items: formData.items.map((item, index) => {
          const mappedItem = {
            id: isEditMode && item.id ? item.id : undefined,
            product_id: item.productId || null,
            product_variant_id: item.variantId || null, // Batch tracking - same as SaleController expects
            batch_number: item.batch_number || null, // Batch tracking
            expiry_date: item.expiry_date || null, // Batch tracking
            description: item.description || "Item",
            qty: parseFloat(item.qty) || 0,
            unit_price: parseFloat(item.unitPrice) || 0,
            sales_price: parseFloat(item.salesPrice) || 0,
            discount_amount: parseFloat(item.discountAmount) || 0,
            discount_percentage: parseFloat(item.discountPercentage) || 0,
            special_discount: parseFloat(item.specialDiscount) || 0,
            total: parseFloat(item.total) || 0,
            total_buying_cost: parseFloat(item.totalBuyingCost) || 0,
            supplier: item.supplier || null,
            category: item.category || null,
            store_location: item.store_location || null,
            mrp: parseFloat(item.mrp) || 0,
            free: parseFloat(item.free) || 0,
          };

          // Debug logging for edit mode
          if (isEditMode) {
            console.log(`Item ${index}:`, {
              originalId: item.id,
              mappedId: mappedItem.id,
              productId: mappedItem.product_id,
              description: mappedItem.description,
              qty: mappedItem.qty,
            });
          }

          return mappedItem;
        }),
        purchaseDetails: {
          method: formData.purchaseDetails.method,
          amount: parseFloat(formData.purchaseDetails.amount) || 0,
          taxPercentage:
            parseFloat(formData.purchaseDetails.taxPercentage) || 0,
        },
        cheque_no: formData.purchaseDetails.method === "cheque" ? formData.cheque_no : undefined,
        bank_name: formData.purchaseDetails.method === "cheque" ? formData.bank_name : undefined,
        issue_date: formData.purchaseDetails.method === "cheque" ? formData.issue_date : undefined,
        status: formData.status,
        quotation_id: selectedQuotation ? selectedQuotation.value : null, // save quotation ID separately
        prepared_by: footerDetails.preparedBy,
        approved_by:
          footerDetails.approvedBy && footerDetails.approvedBy.value
            ? footerDetails.approvedBy.value
            : null,
        // Add redeem_points to payload
        redeem_points: redeemPoints,
      };

      console.log("Submitting payload:", {
        ...payload,
        prepared_by: footerDetails.preparedBy,
        approved_by: footerDetails.approvedBy,
      });

      try {
        if (isEditMode) {
          await onUpdateInvoice(payload, formData.id);
          toast.success("Invoice updated successfully!");
        } else {
          await onGenerateInvoice(payload);
          toast.success("Invoice created successfully!");
          // Auto-delete quotation if used
          if (selectedQuotation && selectedQuotation.value) {
            try {
              await axios.delete(`/api/quotations/${selectedQuotation.value}`);
              toast.success("Quotation deleted automatically after invoice creation.");
              fetchQuotations(); // Refresh the quotations list after deletion
            } catch (deleteErr) {
              toast.error("Invoice created, but failed to delete quotation.");
              console.error("Failed to delete quotation after invoice:", deleteErr);
            }
          }
          localStorage.removeItem(draftKey);
          setFormData({
            invoice: {
              no: "",
              date: format(new Date(), "yyyy-MM-dd"),
              time: format(new Date(), "HH:mm"),
            },
            customer: { id: null, name: "", address: "", phone: "", email: "" },
            items: [],
            purchaseDetails: { method: "cash", amount: 0, taxPercentage: 0 },
            cheque_no: "",
            bank_name: "",
            issue_date: "",
            status: "pending",
            id: null,
          });
          setErrors({});
          invoiceNoRef.current?.focus();
        }
        // Update status after save based on paid amount and grand total
        const amountPaid = parseFloat(formData.purchaseDetails.amount) || 0;
        const subtotal = calculateSubtotal();
        const tax = calculateTax(subtotal);
        const grandTotal = subtotal + tax;
        if (formData.purchaseDetails.method === "credit") {
          if (amountPaid >= grandTotal) {
            setFormData((prev) => ({ ...prev, status: "paid" }));
          } else if (amountPaid > 0) {
            setFormData((prev) => ({ ...prev, status: "partial" }));
          } else {
            setFormData((prev) => ({ ...prev, status: "pending" }));
          }
        } else {
          if (amountPaid >= grandTotal) {
            setFormData((prev) => ({ ...prev, status: "paid" }));
          } else {
            setFormData((prev) => ({ ...prev, status: "partial" }));
          }
        }
      } catch (error) {
        const message =
          error.response?.data?.message || "Failed to save invoice.";
        const details = error.response?.data?.errors
          ? Object.entries(error.response.data.errors)
              .map(([field, messages]) => `${field}: ${messages.join(", ")}`)
              .join("\n")
          : "No detailed errors provided.";
        console.error("API Error Details:", {
          message,
          details,
          response: error.response?.data,
          fullError: error,
        });
        toast.error(`${message}\n${details}`);
      } finally {
        setLoading(false);
      }
    },
    [formData, errors, footerDetails.approvedBy, validateForm]
  );

  useEffect(() => {
    const handleKeyDown = (e) => {
      if ((e.ctrlKey || e.metaKey) && e.key === "s") {
        e.preventDefault();
        handleSubmit(e);
      } else if (e.key === "Escape") {
        e.preventDefault();
        onCancel();
      }
    };
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [handleSubmit, onCancel]);

  const getFieldOrder = useCallback(() => {
    const fields = [
      { ref: quotationSelectRef, name: "quotationNo", type: "select" },
      { ref: invoiceNoRef, name: "invoiceNo", type: "input" },
      { ref: invoiceDateRef, name: "invoiceDate", type: "input" },
      { ref: invoiceTimeRef, name: "invoiceTime", type: "input" },
      { ref: customerNameRef, name: "customerName", type: "select" },
      { ref: customerAddressRef, name: "customerAddress", type: "input" },
      { ref: customerPhoneRef, name: "customerPhone", type: "input" },
      { ref: customerEmailRef, name: "customerEmail", type: "input" },
      { ref: preparedByRef, name: "preparedBy", type: "input" },
      { ref: approvedByRef, name: "approvedBy", type: "select" },
      // Add new item input refs here for enter key navigation
      { ref: newItemProductSelectRef, name: "newItemProduct", type: "select" },
      { ref: newItemQtyRef, name: "newItemQty", type: "input" },
      { ref: newItemFreeRef, name: "newItemFree", type: "input" },
      { ref: newItemUnitPriceRef, name: "newItemUnitPrice", type: "input" },
      {
        ref: newItemDiscountAmountRef,
        name: "newItemDiscountAmount",
        type: "input",
      },
    ];
    formData.items.forEach((_, index) => {
      fields.push(
        {
          ref: itemRefs.current[index]?.description,
          name: `itemDescription${index}`,
          type: "select",
          index,
        },
        {
          ref: itemRefs.current[index]?.qty,
          name: `itemQty${index}`,
          type: "input",
          index,
        },
        {
          ref: itemRefs.current[index]?.unitPrice,
          name: `itemUnitPrice${index}`,
          type: "input",
          index,
        },
        {
          ref: itemRefs.current[index]?.discountAmount,
          name: `itemDiscountAmount${index}`,
          type: "input",
          index,
        },
        {
          ref: itemRefs.current[index]?.discountPercentage,
          name: `itemDiscountPercentage${index}`,
          type: "input",
          index,
        }
      );
    });
    fields.push(
      { ref: purchaseMethodRef, name: "purchaseMethod", type: "select-native" },
      { ref: purchaseAmountRef, name: "purchaseAmount", type: "input" },
      { ref: taxPercentageRef, name: "purchaseTaxPercentage", type: "input" }
    );
    return fields;
  }, [formData.items]);

  const handleEnterKey = (e, currentRef, itemIndex = null) => {
    if (e.key !== "Enter" || e.shiftKey) return;
    e.preventDefault();

    const fields = getFieldOrder();
    const currentFieldIndex = fields.findIndex(
      (field) => field.ref?.current === currentRef.current
    );
    if (currentFieldIndex === -1) return;

    const currentField = fields[currentFieldIndex];
    if (
      currentField.type === "select" &&
      currentField.name === "customerName" &&
      !formData.customer.name
    )
      return;
    if (
      currentField.type === "select" &&
      currentField.name.startsWith("itemDescription") &&
      !formData.items[itemIndex]?.productId
    )
      return;

    // Add new item if Enter pressed on discountAmount of last item
    if (
      currentField.name === `itemDiscountAmount${formData.items.length - 1}`
    ) {
      addItem();
      return;
    }
    if (currentField.name === `itemQty${formData.items.length - 1}`) {
      addItem();
      return;
    }

    // Custom navigation for approved by select
    if (currentField.ref === approvedByRef) {
      newItemProductSelectRef.current?.focus();
      return;
    }

    // Custom navigation for new item form
    if (currentField.ref === newItemProductSelectRef) {
      newItemQtyRef.current?.focus();
      newItemQtyRef.current?.select();
      return;
    }
    if (currentField.ref === newItemQtyRef) {
      newItemFreeRef.current?.focus();
      newItemFreeRef.current?.select();
      return;
    }
    if (currentField.ref === newItemFreeRef) {
      newItemUnitPriceRef.current?.focus();
      newItemUnitPriceRef.current?.select();
      return;
    }
    if (currentField.ref === newItemUnitPriceRef) {
      if (newItem.productId && newItem.qty > 0) {
        addItemButtonRef.current?.focus();
      } else {
        // Focus back to product select if item is not valid
        newItemProductSelectRef.current?.focus();
      }
      return;
    }

    // Custom navigation for payment method, amount paid, tax, and save button
    if (currentField.ref === purchaseMethodRef) {
      if (formData.purchaseDetails.method === "cheque") {
        chequeNoInputRef.current?.focus();
        chequeNoInputRef.current?.select();
      } else {
        purchaseAmountRef.current?.focus();
        purchaseAmountRef.current?.select();
      }
      return;
    }
    if (currentField.ref === purchaseAmountRef) {
      taxPercentageRef.current?.focus();
      taxPercentageRef.current?.select();
      return;
    }
    if (currentField.ref === taxPercentageRef) {
      submitButtonRef.current?.focus();
      return;
    }

    for (let i = currentFieldIndex + 1; i < fields.length; i++) {
      const nextField = fields[i];
      if (nextField.ref?.current) {
        nextField.ref.current.focus();
        if (nextField.type === "input") nextField.ref.current.select?.();
        break;
      }
    }
  };

  const customerOptions = useMemo(() => {
    if (!customers || customers.length === 0) return [];
    return customers.map((c) => ({
      value: c.id ?? c.customer_id ?? null,
      label: c.customer_name || c.name || "Unknown Customer",
    }));
  }, [customers]);

  // const filteredProductOptions = useMemo(() => {
  //   // Flatten products and their variants into individual options
  //   const options = [];
  //   products.forEach((product) => {
  //     if (product.variants && product.variants.length > 0) {
  //       product.variants.forEach((variant) => {
  //         const categoryName = product.category || "Unknown";
  //         // Create display name for batch-wise matching
  //         const batchInfoForDiscount = variant.batch_number
  //           ? ` (Batch: ${variant.batch_number})`
  //           : "";
  //         const expiryInfoForDiscount = variant.expiry_date
  //           ? ` (Exp: ${variant.expiry_date.split("T")[0]})`
  //           : "";
  //         const displayNameForDiscount = `${product.product_name}${batchInfoForDiscount}${expiryInfoForDiscount}`;

  //         const productScheme = discountSchemes.find((scheme) => {
  //           if (!scheme.active || scheme.applies_to !== "product") return false;
  //           const target = scheme.target?.trim().toLowerCase();
  //           const productName = product.product_name?.trim().toLowerCase();
  //           return (
  //             target === productName ||
  //             target === displayNameForDiscount.trim().toLowerCase()
  //           );
  //         });
  //         const categoryScheme = discountSchemes.find((scheme) => {
  //           if (!scheme.active || scheme.applies_to !== "category")
  //             return false;
  //           return (
  //             scheme.target?.trim().toLowerCase() ===
  //             categoryName?.trim().toLowerCase()
  //           );
  //         });

  //         let discountInfo = "";
  //         if (productScheme) {
  //           discountInfo = `, Product Discount: ${
  //             productScheme.type === "percentage"
  //               ? `${productScheme.value}%`
  //               : `LKR ${productScheme.value}`
  //           }`;
  //         } else if (categoryScheme) {
  //           discountInfo = `, Category Discount: ${
  //             categoryScheme.type === "percentage"
  //               ? `${categoryScheme.value}%`
  //               : `LKR ${categoryScheme.value}`
  //           }`;
  //         }

  //         const batchInfo = variant.batch_number
  //           ? `batch ${variant.batch_number}`
  //           : "";
  //         const expiryInfo = variant.expiry_date
  //           ? `Exp: ${variant.expiry_date.split("T")[0]}`
  //           : "";
  //         const variantLabel =
  //           batchInfo && expiryInfo
  //             ? `${batchInfo}, ${expiryInfo}`
  //             : batchInfo || expiryInfo;

  //         options.push({
  //           value: `${product.product_id}-${variant.product_variant_id}`,
  //           label: `${product.product_name}${variantLabel ? ` (${variantLabel})` : ""}`,
  //           description: `${product.product_name} - Stock: ${variant.opening_stock_quantity ?? "N/A"}, Category: ${categoryName}${discountInfo}`,
  //           stock: variant.opening_stock_quantity,
  //           mrp: variant.mrp,
  //           salesPrice: variant.sales_price,
  //           productId: product.product_id,
  //           variantId: variant.product_variant_id,
  //           batchNumber: variant.batch_number,
  //           expiryDate: variant.expiry_date,
  //         });
  //       });
  //     } else {
  //       // No variants, add product as option
  //       const categoryName = product.category || "Unknown";
  //       const productScheme = discountSchemes.find((scheme) => {
  //         if (!scheme.active || scheme.applies_to !== "product") return false;
  //         const target = scheme.target?.trim().toLowerCase();
  //         const productName = product.product_name?.trim().toLowerCase();
  //         // For products without variants, only match the product name
  //         return target === productName;
  //       });
  //       const categoryScheme = discountSchemes.find((scheme) => {
  //         if (!scheme.active || scheme.applies_to !== "category") return false;
  //         return (
  //           scheme.target?.trim().toLowerCase() ===
  //           categoryName?.trim().toLowerCase()
  //         );
  //       });

  //       let discountInfo = "";
  //       if (productScheme) {
  //         discountInfo = `, Product Discount: ${
  //           productScheme.type === "percentage"
  //             ? `${productScheme.value}%`
  //             : `LKR ${productScheme.value}`
  //         }`;
  //       } else if (categoryScheme) {
  //         discountInfo = `, Category Discount: ${
  //           categoryScheme.type === "percentage"
  //             ? `${categoryScheme.value}%`
  //             : `LKR ${categoryScheme.value}`
  //         }`;
  //       }

  //       options.push({
  //         value: product.product_id,
  //         label: `${product.product_name} (${product.description || "No description"})`,
  //         description: `${product.product_name} - Stock: ${product.opening_stock_quantity ?? "N/A"}, Category: ${categoryName}${discountInfo}`,
  //         stock: product.opening_stock_quantity,
  //         mrp: product.mrp,
  //         salesPrice: product.sales_price,
  //         productId: product.product_id,
  //         variantId: null,
  //         batchNumber: null,
  //       });
  //     }
  //   });

  //   // Filter options by searchTerm
  //   const filtered = options.filter((option) =>
  //     option.label.toLowerCase().includes(searchTerm.toLowerCase())
  //   );

  //   return filtered;
  // }, [products, discountSchemes, searchTerm]);

  // const filteredProductOptions = useMemo(() => {
  //   // Flatten products and their variants into individual options
  //   const options = [];
  //   products.forEach((product) => {
  //     // Filter by selected supplier if set
  //     if (selectedSupplier && product.supplier !== selectedSupplier) return;
  //     if (product.variants && product.variants.length > 0) {
  //       product.variants.forEach((variant) => {
  //         const categoryName = product.category || "Unknown";
  //         // Create display name for batch-wise matching
  //         const batchInfoForDiscount = variant.batch_number
  //           ? ` (Batch: ${variant.batch_number})`
  //           : "";
  //         const expiryInfoForDiscount = variant.expiry_date
  //           ? ` (Exp: ${variant.expiry_date.split("T")[0]})`
  //           : "";
  //         const displayNameForDiscount = `${product.product_name}${batchInfoForDiscount}${expiryInfoForDiscount}`;
  
  //         const productScheme = discountSchemes.find((scheme) => {
  //           if (!scheme.active || scheme.applies_to !== "product") return false;
  //           const target = scheme.target?.trim().toLowerCase();
  //           const productName = product.product_name?.trim().toLowerCase();
  //           return (
  //             target === productName ||
  //             target === displayNameForDiscount.trim().toLowerCase()
  //           );
  //         });
  //         const categoryScheme = discountSchemes.find((scheme) => {
  //           if (!scheme.active || scheme.applies_to !== "category")
  //             return false;
  //           return (
  //             scheme.target?.trim().toLowerCase() ===
  //             categoryName?.trim().toLowerCase()
  //           );
  //         });
  
  //         let discountInfo = "";
  //         if (productScheme) {
  //           discountInfo = `, Product Discount: ${
  //             productScheme.type === "percentage"
  //               ? `${productScheme.value}%`
  //               : `LKR ${productScheme.value}`
  //           }`;
  //         } else if (categoryScheme) {
  //           discountInfo = `, Category Discount: ${
  //             categoryScheme.type === "percentage"
  //               ? `${categoryScheme.value}%`
  //               : `LKR ${categoryScheme.value}`
  //           }`;
  //         }
  
  //         const batchInfo = variant.batch_number
  //           ? `batch ${variant.batch_number}`
  //           : "";
  //         const expiryInfo = variant.expiry_date
  //           ? `Exp: ${variant.expiry_date.split("T")[0]}`
  //           : "";
  //         const variantLabel =
  //           batchInfo && expiryInfo
  //             ? `${batchInfo}, ${expiryInfo}`
  //             : batchInfo || expiryInfo;
  
  //         options.push({
  //           value: `${product.product_id}-${variant.product_variant_id}`,
  //           label: `${product.product_name}${variantLabel ? ` (${variantLabel})` : ""}`,
  //           description: `${product.product_name} - Stock: ${variant.opening_stock_quantity ?? "N/A"}, Category: ${categoryName}${discountInfo}`,
  //           stock: variant.opening_stock_quantity,
  //           mrp: variant.mrp,
  //           salesPrice: variant.sales_price,
  //           productId: product.product_id,
  //           variantId: variant.product_variant_id,
  //           batchNumber: variant.batch_number,
  //           expiryDate: variant.expiry_date,
  //         });
  //       });
  //     } else {
  //       // No variants, add product as option
  //       const categoryName = product.category || "Unknown";
  //       const productScheme = discountSchemes.find((scheme) => {
  //         if (!scheme.active || scheme.applies_to !== "product") return false;
  //         const target = scheme.target?.trim().toLowerCase();
  //         const productName = product.product_name?.trim().toLowerCase();
  //         // For products without variants, only match the product name
  //         return target === productName;
  //       });
  //       const categoryScheme = discountSchemes.find((scheme) => {
  //         if (!scheme.active || scheme.applies_to !== "category") return false;
  //         return (
  //           scheme.target?.trim().toLowerCase() ===
  //           categoryName?.trim().toLowerCase()
  //         );
  //       });
  
  //       let discountInfo = "";
  //       if (productScheme) {
  //         discountInfo = `, Product Discount: ${
  //           productScheme.type === "percentage"
  //             ? `${productScheme.value}%`
  //             : `LKR ${productScheme.value}`
  //         }`;
  //       } else if (categoryScheme) {
  //         discountInfo = `, Category Discount: ${
  //           categoryScheme.type === "percentage"
  //             ? `${categoryScheme.value}%`
  //             : `LKR ${categoryScheme.value}`
  //         }`;
  //       }
  
  //       options.push({
  //         value: product.product_id,
  //         label: `${product.product_name} (${product.description || "No description"})`,
  //         description: `${product.product_name} - Stock: ${product.opening_stock_quantity ?? "N/A"}, Category: ${categoryName}${discountInfo}`,
  //         stock: product.opening_stock_quantity,
  //         mrp: product.mrp,
  //         salesPrice: product.sales_price,
  //         productId: product.product_id,
  //         variantId: null,
  //         batchNumber: null,
  //       });
  //     }
  //   });
  
  //   // Filter options by searchTerm
  //   const filtered = options.filter((option) =>
  //     option.label.toLowerCase().includes(searchTerm.toLowerCase())
  //   );
  
  //   return filtered;
  // }, [products, discountSchemes, searchTerm, selectedSupplier]);
  
  const filteredProductOptions = useMemo(() => {
    // Flatten products and their variants into individual options
    const options = [];
    products.forEach((product) => {
      // Filter by selected supplier if set
      if (selectedSupplier && product.supplier !== selectedSupplier) return;
      if (product.variants && product.variants.length > 0) {
        product.variants.forEach((variant) => {
          const categoryName = product.category || "Unknown";
          // Create display name for batch-wise matching
          const batchInfoForDiscount = variant.batch_number
            ? ` (Batch: ${variant.batch_number})`
            : "";
          const expiryInfoForDiscount = variant.expiry_date
            ? ` (Exp: ${variant.expiry_date.split("T")[0]})`
            : "";
          const displayNameForDiscount = `${product.product_name}${batchInfoForDiscount}${expiryInfoForDiscount}`;
  
          const productScheme = discountSchemes.find((scheme) => {
            if (!scheme.active || scheme.applies_to !== "product") return false;
            const target = scheme.target?.trim().toLowerCase();
            const productName = product.product_name?.trim().toLowerCase();
            return (
              target === productName ||
              target === displayNameForDiscount.trim().toLowerCase()
            );
          });
          const categoryScheme = discountSchemes.find((scheme) => {
            if (!scheme.active || scheme.applies_to !== "category")
              return false;
            return (
              scheme.target?.trim().toLowerCase() ===
              categoryName?.trim().toLowerCase()
            );
          });
  
          let discountInfo = "";
          if (productScheme) {
            discountInfo = `, Product Discount: ${
              productScheme.type === "percentage"
                ? `${productScheme.value}%`
                : `LKR ${productScheme.value}`
            }`;
          } else if (categoryScheme) {
            discountInfo = `, Category Discount: ${
              categoryScheme.type === "percentage"
                ? `${categoryScheme.value}%`
                : `LKR ${categoryScheme.value}`
            }`;
          }
  
          const batchInfo = variant.batch_number
            ? `batch ${variant.batch_number}`
            : "";
          const expiryInfo = variant.expiry_date
            ? `Exp: ${variant.expiry_date.split("T")[0]}`
            : "";
          const variantLabel =
            batchInfo && expiryInfo
              ? `${batchInfo}, ${expiryInfo}`
              : batchInfo || expiryInfo;
  
          options.push({
            value: `${product.product_id}-${variant.product_variant_id}`,
            label: `${product.product_name}${variantLabel ? ` (${variantLabel})` : ""}`,
            description: `${product.product_name} - Stock: ${variant.opening_stock_quantity ?? "N/A"}, Category: ${categoryName}${discountInfo}`,
            stock: variant.opening_stock_quantity,
            mrp: variant.mrp,
            salesPrice: variant.sales_price,
            productId: product.product_id,
            variantId: variant.product_variant_id,
            batchNumber: variant.batch_number,
            expiryDate: variant.expiry_date,
          });
        });
      } else {
        // No variants, add product as option
        const categoryName = product.category || "Unknown";
        const productScheme = discountSchemes.find((scheme) => {
          if (!scheme.active || scheme.applies_to !== "product") return false;
          const target = scheme.target?.trim().toLowerCase();
          const productName = product.product_name?.trim().toLowerCase();
          // For products without variants, only match the product name
          return target === productName;
        });
        const categoryScheme = discountSchemes.find((scheme) => {
          if (!scheme.active || scheme.applies_to !== "category") return false;
          return (
            scheme.target?.trim().toLowerCase() ===
            categoryName?.trim().toLowerCase()
          );
        });
  
        let discountInfo = "";
        if (productScheme) {
          discountInfo = `, Product Discount: ${
            productScheme.type === "percentage"
              ? `${productScheme.value}%`
              : `LKR ${productScheme.value}`
          }`;
        } else if (categoryScheme) {
          discountInfo = `, Category Discount: ${
            categoryScheme.type === "percentage"
              ? `${categoryScheme.value}%`
              : `LKR ${categoryScheme.value}`
          }`;
        }
  
        options.push({
          value: product.product_id,
          label: `${product.product_name} (${product.description || "No description"})`,
          description: `${product.product_name} - Stock: ${product.opening_stock_quantity ?? "N/A"}, Category: ${categoryName}${discountInfo}`,
          stock: product.opening_stock_quantity,
          mrp: product.mrp,
          salesPrice: product.sales_price,
          productId: product.product_id,
          variantId: null,
          batchNumber: null,
        });
      }
    });
  
    // Read the setting from localStorage
    const startsWithOnly = localStorage.getItem("productSearchStartsWithOnly") === "true";
    // Filter options by searchTerm
    const filtered = options.filter((option) => {
      const label = option.label.toLowerCase();
      const search = searchTerm.toLowerCase();
      if (startsWithOnly) {
        return label.startsWith(search);
      } else {
        return label.includes(search);
      }
    });
  
    return filtered;
  }, [products, discountSchemes, searchTerm, selectedSupplier]);
  
  const getSelectStyles = (hasError) => ({
    control: (provided, state) => ({
      ...provided,
      borderColor: hasError
        ? "#ef4444"
        : state.isFocused
          ? "#3b82f6"
          : "#d1d5db",
      boxShadow: state.isFocused
        ? "0 0 0 1px #3b82f6"
        : hasError
          ? "0 0 0 1px #ef4444"
          : "none",
      "&:hover": { borderColor: hasError ? "#ef4444" : "#9ca3af" },
      minHeight: "42px",
    }),
    menu: (provided) => ({ ...provided, zIndex: 50 }),
    option: (provided, state) => ({
      ...provided,
      backgroundColor: state.isSelected
        ? "#dbeafe"
        : state.isFocused
          ? "#eff6ff"
          : "white",
    }),
    indicatorSeparator: () => ({ display: "none" }),
  });

  const subtotal = useMemo(() => calculateSubtotal(), [calculateSubtotal]);
  const tax = useMemo(() => calculateTax(subtotal), [calculateTax, subtotal]);
  const total = useMemo(() => calculateTotal(), [calculateTotal]);
  const balance = useMemo(() => calculateBalance(), [calculateBalance]);

  const assignRef = (index, field, element) => {
    if (!itemRefs.current[index]) itemRefs.current[index] = {};
    itemRefs.current[index][field] = { current: element };
  };

  // Add after other useState declarations
  const [customerLoyaltyPoints, setCustomerLoyaltyPoints] = useState(null);
  const [loyaltyLoading, setLoyaltyLoading] = useState(false);
  const [loyaltyError, setLoyaltyError] = useState(null);

  // Add state for redeem points
  const [redeemPoints, setRedeemPoints] = useState(0);
  const [redeemPointsError, setRedeemPointsError] = useState("");

  const grandTotal = total - calculateTotalDiscount();
  const totalPaid = (parseFloat(formData.purchaseDetails.amount) || 0) + (parseFloat(redeemPoints) || 0);
  const balanceDue = grandTotal - totalPaid;

  useEffect(() => {
    setRedeemPoints(0); // Reset redeem amount when items change
  }, [formData.items]);

  useEffect(() => {
    let clamped = redeemPoints;
    if (clamped < 0) clamped = 0;
    if (customerLoyaltyPoints !== null && clamped > customerLoyaltyPoints) clamped = customerLoyaltyPoints;
    if (clamped !== redeemPoints) setRedeemPoints(clamped);
  }, [redeemPoints, customerLoyaltyPoints, formData.items]);

  return (
    <ErrorBoundary>
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-gray-900 bg-opacity-60 backdrop-blur-sm">
        <div className="relative flex flex-col w-full h-full overflow-y-auto bg-white dark:bg-gray-800">
          <h3 className="p-6 text-2xl font-bold text-blue-600 border-b border-gray-200">
            {isEditMode ? "Edit Invoice" : "Create New Invoice"}
          </h3>
          <form
            id="invoiceForm"
            onSubmit={handleSubmit}
            noValidate
            className="w-full h-full p-6 overflow-y-auto bg-gray-50"
          >
            <div className="p-4 mb-6 bg-white border rounded-lg shadow-sm">
              <h4 className="pb-2 mb-3 text-lg font-semibold border-b">
                Invoice Details
              </h4>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                <div>
                  <label
                    htmlFor="quotationNo"
                    className="block mb-1 text-sm font-medium"
                  >
                    Quotation No (Optional)
                  </label>
                  <Select
                    id="quotationNo"
                    ref={quotationSelectRef}
                    options={quotations}
                    value={selectedQuotation}
                    onChange={handleQuotationChange}
                    isClearable
                    placeholder="Select a quotation"
                    styles={unifiedSelectStyles(!!errors.quotationNo)}
                    menuIsOpen={quotationMenuIsOpen}
                    onMenuOpen={() => setQuotationMenuIsOpen(true)}
                    onMenuClose={() => setQuotationMenuIsOpen(false)}
                    onKeyDown={(e) => {
                      // Only handle navigation when menu is closed and no option is highlighted
                      if (e.key === "Enter" && !quotationMenuIsOpen) {
                        // If a quotation is already selected, move to next field
                        if (selectedQuotation) {
                          e.preventDefault();
                          invoiceNoRef.current?.focus();
                          invoiceNoRef.current?.select?.();
                        }
                        // If no quotation selected, let react-select handle the Enter to open menu
                      } else if (
                        e.key === "ArrowDown" &&
                        !quotationMenuIsOpen
                      ) {
                        e.preventDefault();
                        setQuotationMenuIsOpen(true);
                      } else if (e.key === "Tab") {
                        // Allow Tab to move to next field
                        if (!quotationMenuIsOpen) {
                          invoiceNoRef.current?.focus();
                        }
                      }
                      // Let react-select handle all other keyboard events (Arrow keys, Enter for selection, etc.)
                    }}
                  />
                </div>

                <div>
                  <label
                    htmlFor="invoiceNo"
                    className="block mb-1 text-sm font-medium"
                  >
                    Invoice No
                  </label>
                  <input
                    id="invoiceNo"
                    ref={invoiceNoRef}
                    type="text"
                    value={formData.invoice.no || ""}
                    onChange={(e) => handleInputChange(e, "invoice", "no")}
                    onKeyDown={(e) => handleEnterKey(e, invoiceNoRef)}
                    className={`w-full p-2.5 border rounded-md focus:outline-none ${
                      errors.invoiceNo
                        ? "border-red-500"
                        : "border-gray-300 focus:border-blue-500"
                    }`}
                    placeholder="INV-2025-001"
                    readOnly
                  />
                </div>
                <div>
                  <label
                    htmlFor="invoiceDate"
                    className="block mb-1 text-sm font-medium"
                  >
                    Date <span className="text-red-500">*</span>
                  </label>
                  <input
                    id="invoiceDate"
                    ref={invoiceDateRef}
                    type="date"
                    value={formData.invoice.date || ""}
                    onChange={(e) => handleInputChange(e, "invoice", "date")}
                    onKeyDown={(e) => handleEnterKey(e, invoiceDateRef)}
                    className={`w-full p-2.5 border rounded-md focus:outline-none ${
                      errors.invoiceDate
                        ? "border-red-500"
                        : "border-gray-300 focus:border-blue-500"
                    }`}
                    required
                  />
                  {errors.invoiceDate && (
                    <p className="mt-1 text-xs text-red-600">
                      {errors.invoiceDate}
                    </p>
                  )}
                </div>
                <div>
                  <label
                    htmlFor="invoiceTime"
                    className="block mb-1 text-sm font-medium"
                  >
                    Time <span className="text-red-500">*</span>
                  </label>
                  <input
                    id="invoiceTime"
                    ref={invoiceTimeRef}
                    type="time"
                    value={formData.invoice.time || ""}
                    onChange={(e) => handleInputChange(e, "invoice", "time")}
                    onKeyDown={(e) => handleEnterKey(e, invoiceTimeRef)}
                    className={`w-full p-2.5 border rounded-md focus:outline-none ${
                      errors.invoiceTime
                        ? "border-red-500"
                        : "border-gray-300 focus:border-blue-500"
                    }`}
                    required
                  />
                  {errors.invoiceTime && (
                    <p className="mt-1 text-xs text-red-600">
                      {errors.invoiceTime}
                    </p>
                  )}
                </div>
                <div>
                  <label htmlFor="supplierSelect" className="block mb-2 text-sm font-medium">
                    Supplier
                  </label>
                  <Select
                    id="supplierSelect"
                    options={supplierOptions}
                    value={supplierOptions.find((opt) => opt.value === selectedSupplier) || supplierOptions[0]}
                    onChange={(option) => setSelectedSupplier(option?.value || null)}
                    isClearable={false}
                    placeholder="All Suppliers"
                    styles={unifiedSelectStyles(false)}
                  />
                </div>
              </div>
            </div>
            <div className="p-4 mb-6 bg-white border rounded-lg shadow-sm">
              <h4 className="pb-2 mb-3 text-lg font-semibold border-b">
                Customer Information
              </h4>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
                <div>
                  <label
                    htmlFor="customerName"
                    className="block mb-1 text-sm font-medium"
                  >
                    Name <span className="text-red-500">*</span>
                  </label>
                  {/* <Select
                    inputId="customerName"
                    ref={customerNameRef}
                    options={customerOptions}
                    value={
                      customerOptions.find(
                        (option) => option.label === formData.customer.name
                      ) || null
                    }
                    onChange={handleCustomerSelect}
                    placeholder={
                      customersLoading ? "Loading..." : "Select customer"
                    }
                    isClearable
                    isSearchable
                    isDisabled={customersLoading}
                    styles={getSelectStyles(!!errors.customerName)}
                    onKeyDown={(e) => handleEnterKey(e, customerNameRef)}
                  /> */}

                  <Select
                    inputId="customerName"
                    ref={customerNameRef}
                    options={customerOptions}
                    value={
                      customerOptions.find(
                        (option) =>
                          option.value === formData.customer.id ||
                          option.value?.toString() ===
                            formData.customer.id?.toString() ||
                          option.label === formData.customer.name
                      ) ||
                      (formData.customer.name
                        ? {
                            value: formData.customer.id,
                            label: formData.customer.name,
                          }
                        : null)
                    }
                    onChange={handleCustomerSelect}
                    placeholder={
                      customersLoading ? "Loading..." : "Select customer"
                    }
                    isClearable
                    isSearchable
                    isDisabled={customersLoading}
                    styles={unifiedSelectStyles(!!errors.customerName)}
                    // Removed onKeyDown handler to allow react-select keyboard navigation and selection
                  />

                  {errors.customerName && (
                    <p className="mt-1 text-xs text-red-600">
                      {errors.customerName}
                    </p>
                  )}
                  {loyaltyLoading && (
                    <div className="mt-1 text-xs text-gray-500">Loading loyalty points...</div>
                  )}
                  {loyaltyError && (
                    <div className="mt-1 text-xs text-red-500">{loyaltyError}</div>
                  )}
                  {!loyaltyLoading && !loyaltyError && customerLoyaltyPoints !== null && (
                    <div className="mt-1 text-xs font-semibold text-blue-600">
                      Loyalty Points: {customerLoyaltyPoints}
                    </div>
                  )}
                </div>
                <div>
                  <label
                    htmlFor="customerAddress"
                    className="block mb-1 text-sm font-medium"
                  >
                    Address
                  </label>
                  <input
                    id="customerAddress"
                    ref={customerAddressRef}
                    type="text"
                    value={formData.customer.address || ""}
                    onChange={(e) =>
                      handleInputChange(e, "customer", "address")
                    }
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        e.preventDefault();
                        customerPhoneRef.current?.focus();
                        customerPhoneRef.current?.select?.();
                      } else {
                        handleEnterKey(e, customerAddressRef);
                      }
                    }}
                    className="w-full p-2.5 border border-gray-300 rounded-md focus:outline-none focus:border-blue-500"
                    placeholder="123 Main St"
                  />
                </div>
                <div>
                  <label
                    htmlFor="customerPhone"
                    className="block mb-1 text-sm font-medium"
                  >
                    Phone
                  </label>
                  <input
                    id="customerPhone"
                    ref={customerPhoneRef}
                    type="tel"
                    value={formData.customer.phone || ""}
                    onChange={(e) => handleInputChange(e, "customer", "phone")}
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        e.preventDefault();
                        customerEmailRef.current?.focus();
                        customerEmailRef.current?.select?.();
                      } else {
                        handleEnterKey(e, customerPhoneRef);
                      }
                    }}
                    className="w-full p-2.5 border border-gray-300 rounded-md focus:outline-none focus:border-blue-500"
                    placeholder="+94 ************"
                  />
                </div>
                <div>
                  <label
                    htmlFor="customerEmail"
                    className="block mb-1 text-sm font-medium"
                  >
                    Email
                  </label>
                  <input
                    id="customerEmail"
                    ref={customerEmailRef}
                    type="email"
                    value={formData.customer.email || ""}
                    onChange={(e) => handleInputChange(e, "customer", "email")}
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        e.preventDefault();
                        preparedByRef.current?.focus();
                        preparedByRef.current?.select?.();
                      } else {
                        handleEnterKey(e, filteredProductOptions);
                      }
                    }}
                    className={`w-full p-2.5 border rounded-md focus:outline-none ${
                      errors.customerEmail
                        ? "border-red-500"
                        : "border-gray-300 focus:border-blue-500"
                    }`}
                    placeholder="<EMAIL>"
                  />
                  {errors.customerEmail && (
                    <p className="mt-1 text-xs text-red-600">
                      {errors.customerEmail}
                    </p>
                  )}
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              {!isEditMode && (
                <>
                  <div>
                    <label className="block mb-1 font-semibold text-gray-700">
                      Prepared By:
                    </label>
                    <input
                      type="text"
                      ref={preparedByRef}
                      value={footerDetails.preparedBy || ""}
                      onChange={handlePreparedByChange}
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault();
                          focusApprovedBySelect();
                        }
                      }}
                      className="w-full p-2 border border-gray-300 rounded-md"
                      placeholder="Prepared By"
                    />
                  </div>
                  <div>
                    <label className="block mb-1 font-semibold text-gray-700">
                      Approved By:
                    </label>
                    <Select
                      ref={approvedByRef}
                      value={footerDetails.approvedBy}
                      onChange={handleApprovedByChange}
                      options={users.map((user) => ({
                        value: user.name,
                        label: user.name,
                      }))}
                      isClearable={true}
                      placeholder="Select Approved By"
                      styles={unifiedSelectStyles(false)}
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault();
                          if (itemRefs.current.length > 0) {
                            const firstItemDescriptionRef =
                              itemRefs.current[0]?.description?.current;
                            if (firstItemDescriptionRef) {
                              firstItemDescriptionRef.focus();
                              if (
                                typeof firstItemDescriptionRef.select ===
                                "function"
                              ) {
                                firstItemDescriptionRef.select();
                              }
                              return;
                            }
                          }
                          newItemProductSelectRef.current?.focus();
                          newItemProductSelectRef.current?.select?.();
                        }
                      }}
                    />
                  </div>
                </>
              )}
            </div>
            {/* New Item Selection Section */}
            <div className="p-4 mb-6 bg-white border border-gray-200 rounded-lg shadow-sm">
              <h4 className="pb-2 mb-4 text-lg font-semibold text-gray-800 border-b border-gray-200">
                Add New Item
              </h4>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-12">
                {/* Product Selection */}
                <div className="md:col-span-5">
                  <label className="block mb-1 text-sm font-medium text-gray-700">
                    Product <span className="text-red-500">*</span>
                  </label>
                  <Select
                    ref={newItemProductSelectRef}
                    options={filteredProductOptions}
                    value={
                      newItem.productId
                        ? filteredProductOptions.find((option) => {
                            // Handle both single product ID and product-variant combination
                            if (newItem.variantId) {
                              return (
                                option.value ===
                                `${newItem.productId}-${newItem.variantId}`
                              );
                            } else {
                              return (
                                option.value === newItem.productId ||
                                option.value === newItem.productId.toString()
                              );
                            }
                          }) || null
                        : null
                    }
                    placeholder="Search or select product"
                    isClearable
                    isSearchable
                    onChange={handleNewItemProductSelect}
                    onInputChange={(value) => setSearchTerm(value)}
                    styles={unifiedSelectStyles(!!errors.newItemDescription)}
                    className="basic-multi-select"
                    classNamePrefix="select"
                    noOptionsMessage={() => "No products found"}
                    loadingMessage={() => "Loading..."}
                    onKeyDown={(e) => {
                      if (e.key === "Enter" && newItem.productId) {
                        e.preventDefault();
                        newItemQtyRef.current?.focus();
                        newItemQtyRef.current?.select();
                      }
                    }}
                  />
                  {errors.newItemDescription && (
                    <p className="mt-1 text-xs text-red-600">
                      {errors.newItemDescription}
                    </p>
                  )}
                </div>

                {/* Quantity */}
                <div className="md:col-span-2">
                  <label className="block mb-1 text-sm font-medium text-gray-700">
                    Quantity <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="number"
                    min="1"
                    step="1"
                    ref={newItemQtyRef}
                    className={`w-full p-2 border rounded-md focus:ring-blue-500 focus:border-blue-500 ${
                      errors.newItemQty ? "border-red-500" : "border-gray-300"
                    }`}
                    value={newItem.qty}
                    onChange={(e) => handleNewItemInputChange(e, "qty")}
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        e.preventDefault();
                        newItemFreeRef.current?.focus();
                        newItemFreeRef.current?.select();
                      }
                    }}
                  />
                  {errors.newItemQty && (
                    <p className="mt-1 text-xs text-red-600">
                      {errors.newItemQty}
                    </p>
                  )}
                </div>
                {/* Free */}
                <div>
                  <label className="block mb-1 text-sm font-medium text-gray-700">
                    Free <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="number"
                    min="0"
                    step="0.01"
                    ref={newItemFreeRef}
                    className={`w-full p-2 border rounded-md focus:ring-blue-500 focus:border-blue-500 ${
                      errors.newItemFree ? "border-red-500" : "border-gray-300"
                    }`}
                    value={newItem.free}
                    onChange={(e) => handleNewItemInputChange(e, "free")}
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        e.preventDefault();
                        newItemUnitPriceRef.current?.focus();
                        newItemUnitPriceRef.current?.select();
                      }
                    }}
                  />
                </div>
                {/* Unit Price */}
                <div className="md:col-span-2">
                  <label className="block mb-1 text-sm font-medium text-gray-700">
                    MRP <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="number"
                    min="0"
                    step="0.01"
                    ref={newItemUnitPriceRef}
                    className={`w-full p-2 border rounded-md focus:ring-blue-500 focus:border-blue-500 ${
                      errors.newItemUnitPrice
                        ? "border-red-500"
                        : "border-gray-300"
                    }`}
                    value={newItem.unitPrice}
                    onChange={(e) => handleNewItemInputChange(e, "unitPrice")}
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        e.preventDefault();
                        if (newItem.productId && newItem.qty > 0) {
                          addItemButtonRef.current?.focus();
                        } else {
                          newItemProductSelectRef.current?.focus();
                        }
                      }
                    }}
                  />
                  {errors.newItemUnitPrice && (
                    <p className="mt-1 text-xs text-red-600">
                      {errors.newItemUnitPrice}
                    </p>
                  )}
                </div>

                {/* Discount
                <div className="md:col-span-2">
                  <label className="block mb-1 text-sm font-medium text-gray-700">
                    Discount
                  </label>
                  <div className="flex">
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      ref={newItemDiscountAmountRef}
                      className={`w-full p-2 border border-gray-300 rounded-l-md focus:ring-blue-500 focus:border-blue-500 ${
                        errors.newItemDiscountAmount
                          ? "border-red-500"
                          : "border-gray-300"
                      }`}
                      placeholder="Amount"
                      value={newItem.discountAmount}
                      onChange={(e) =>
                        handleNewItemInputChange(e, "discountAmount")
                      }
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault();
                          handleAddNewItem();
                        }
                      }}
                    />
                    <span className="inline-flex items-center px-3 text-sm text-gray-500 bg-gray-100 border border-l-0 border-gray-300 rounded-r-md">
                      LKR
                    </span>
                  </div>
                  {errors.newItemDiscountAmount && (
                    <p className="mt-1 text-xs text-red-600">
                      {errors.newItemDiscountAmount}
                    </p>
                  )}
                </div> */}

                {/* Add Button */}
                <div className="flex items-end md:col-span-1">
                  <button
                    ref={addItemButtonRef}
                    type="button"
                    onClick={handleAddNewItem}
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        e.preventDefault();
                        handleAddNewItem();
                      }
                    }}
                    className="w-full h-[42px] px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors flex items-center justify-center"
                  >
                    <svg
                      className="w-4 h-4 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                      />
                    </svg>
                    Add
                  </button>
                </div>
              </div>
            </div>

            {/* Items Table Section */}
            <div className="p-4 mb-6 bg-white border border-gray-200 rounded-lg shadow-sm">
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-lg font-semibold text-gray-800">
                  Invoice Items
                </h4>
                <div className="text-sm text-gray-500">
                  {formData.items.length} item(s)
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="w-2/5 px-2 py-2 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                        Product Name
                      </th>
                      <th className="w-16 px-1 py-2 text-xs font-medium tracking-wider text-center text-gray-500 uppercase">
                        Qty
                      </th>
                      <th className="w-20 px-1 py-2 text-xs font-medium tracking-wider text-center text-gray-500 uppercase">
                        MRP
                      </th>
                      <th className="w-20 px-1 py-2 text-xs font-medium tracking-wider text-center text-gray-500 uppercase">
                        Price
                      </th>
                      <th className="w-20 px-1 py-2 text-xs font-medium tracking-wider text-center text-gray-500 uppercase">
                        Discount
                      </th>
                      <th className="w-16 px-1 py-2 text-xs font-medium tracking-wider text-center text-gray-500 uppercase">
                        Free
                      </th>
                      <th className="w-20 px-1 py-2 text-xs font-medium tracking-wider text-center text-gray-500 uppercase">
                        Total
                      </th>
                      <th className="w-16 px-1 py-2 text-xs font-medium tracking-wider text-center text-gray-500 uppercase">
                        Action
                      </th>
                    </tr>
                  </thead>

                  <tbody className="bg-white divide-y divide-gray-200">
                    {formData.items.map((item, index) => (
                      <tr key={item.id} className="hover:bg-gray-50">
                        <td className="w-2/5 px-2 py-1">
                          <Select
                            options={products.map((p) => ({
                              value: p.product_id,
                              label: p.product_name,
                            }))}
                            value={
                              products.find(
                                (p) => p.product_id === item.productId
                              )
                                ? {
                                    value: item.productId,
                                    label: products.find(
                                      (p) => p.product_id === item.productId
                                    )?.product_name,
                                  }
                                : null
                            }
                            onChange={(selectedOption) =>
                              handleProductSelect(selectedOption, index)
                            }
                            placeholder="Select product..."
                            className="w-full text-sm"
                            isSearchable
                            styles={{
                              control: (provided) => ({
                                ...provided,
                                minHeight: "32px",
                                fontSize: "14px",
                              }),
                              valueContainer: (provided) => ({
                                ...provided,
                                padding: "2px 6px",
                              }),
                              input: (provided) => ({
                                ...provided,
                                margin: "0px",
                              }),
                              indicatorSeparator: () => ({
                                display: "none",
                              }),
                              indicatorsContainer: (provided) => ({
                                ...provided,
                                height: "32px",
                              }),
                            }}
                          />
                        </td>
                        <td className="w-16 px-1 py-1">
                          <input
                            type="number"
                            min="0"
                            step="0.01"
                            value={item.qty !== undefined && item.qty !== null ? item.qty : 0}
                            onChange={(e) =>
                              handleInputChange(e, "items", "qty", index)
                            }
                            onKeyDown={(e) =>
                              handleEnterKey(e, { current: e.target }, index)
                            }
                            className="w-full p-1 text-xs text-center border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                            style={{ minWidth: "50px" }}
                          />
                        </td>
                        <td className="w-20 px-1 py-1">
                          <input
                            type="number"
                            min="0"
                            step="0.01"
                            value={item.unitPrice !== undefined && item.unitPrice !== null ? item.unitPrice : 0}
                            onChange={(e) =>
                              handleInputChange(e, "items", "unitPrice", index)
                            }
                            onKeyDown={(e) =>
                              handleEnterKey(e, { current: e.target }, index)
                            }
                            className="w-full p-1 text-xs text-center border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                            style={{ minWidth: "60px" }}
                          />
                        </td>
                        <td className="w-20 px-1 py-1">
                          <input
                            type="number"
                            min="0"
                            step="0.01"
                            value={item.salesPrice !== undefined && item.salesPrice !== null ? item.salesPrice : 0}
                            onChange={(e) =>
                              handleInputChange(e, "items", "salesPrice", index)
                            }
                            onKeyDown={(e) =>
                              handleEnterKey(e, { current: e.target }, index)
                            }
                            className="w-full p-1 text-xs text-center border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                            style={{ minWidth: "60px" }}
                          />
                        </td>
                        <td className="w-20 px-1 py-1">
                          <input
                            type="number"
                            min="0"
                            step="0.01"
                            value={item.discountAmount !== undefined && item.discountAmount !== null ? item.discountAmount : 0}
                            onChange={(e) =>
                              handleInputChange(
                                e,
                                "items",
                                "discountAmount",
                                index
                              )
                            }
                            onKeyDown={(e) =>
                              handleEnterKey(e, { current: e.target }, index)
                            }
                            className="w-full p-1 text-xs text-center border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                            style={{ minWidth: "60px" }}
                          />
                        </td>
                        <td className="w-16 px-1 py-1">
                          <input
                            type="number"
                            min="0"
                            step="1"
                            value={item.free !== undefined && item.free !== null ? item.free : 0}
                            onChange={(e) =>
                              handleInputChange(e, "items", "free", index)
                            }
                            onKeyDown={(e) =>
                              handleEnterKey(e, { current: e.target }, index)
                            }
                            className="w-full p-1 text-xs text-center border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                            style={{ minWidth: "50px" }}
                          />
                        </td>
                        <td className="w-20 px-1 py-1 text-xs font-medium text-center">
                          {formatCurrency(item.total || 0)}
                        </td>
                        <td className="w-16 px-1 py-1 text-center">
                          <button
                            type="button"
                            onClick={() => removeItem(index)}
                            className="p-1 text-red-600 rounded hover:text-red-900 focus:outline-none focus:ring-1 focus:ring-red-500"
                            title="Remove item"
                          >
                            <svg
                              className="w-3 h-3"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                              />
                            </svg>
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
              <div className="p-4 bg-white border border-gray-200 rounded-lg shadow-sm md:col-span-2">
                <h4 className="pb-2 mb-3 text-lg font-semibold text-gray-800 border-b border-gray-200">
                  Payment Details
                </h4>
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <div>
                    <label
                      htmlFor="purchaseMethod"
                      className="block mb-1 text-sm font-medium text-gray-700"
                    >
                      Payment Method
                    </label>
                    <select
                      id="purchaseMethod"
                      ref={purchaseMethodRef}
                      value={formData.purchaseDetails.method}
                      onChange={(e) =>
                        handleInputChange(e, "purchaseDetails", "method")
                      }
                      onKeyDown={(e) => handleEnterKey(e, purchaseMethodRef)}
                      className="w-full p-2.5 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="cash">Cash</option>
                      <option value="card">Card</option>
                      <option value="online">Online Payment</option>
                      <option value="cheque">Cheque</option>
                      <option value="credit">Credit</option>
                    </select>
                    {formData.purchaseDetails.method === "cheque" && (
                    <div className="grid w-full grid-row-3">
                      <div>
                        <label htmlFor="cheque_no" className="block mb-1 text-sm font-medium text-gray-700 w-[100px]">
                          Cheque No <span className="text-red-500">*</span>
                        </label>
                        <input
                          ref={chequeNoInputRef}
                          id="cheque_no"
                          type="text"
                          value={formData.cheque_no || ""}
                          onChange={e => setFormData(prev => ({ ...prev, cheque_no: e.target.value }))}
                          onKeyDown={e => {
                            if (e.key === "Enter") {
                              e.preventDefault();
                              bankNameInputRef.current?.focus();
                            }
                          }}
                          className={`w-full p-2.5 border rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${errors.cheque_no ? "border-red-500" : "border-gray-300"}`}
                          required
                        />
                        {errors.cheque_no && <p className="mt-1 text-xs text-red-600">{errors.cheque_no}</p>}
                      </div>
                      <div>
                        <label htmlFor="bank_name" className="block mb-1 text-sm font-medium text-gray-700">
                          Bank Name <span className="text-red-500">*</span>
                        </label>
                        <input
                          ref={bankNameInputRef}
                          id="bank_name"
                          type="text"
                          value={formData.bank_name || ""}
                          onChange={e => setFormData(prev => ({ ...prev, bank_name: e.target.value }))}
                          onKeyDown={e => {
                            if (e.key === "Enter") {
                              e.preventDefault();
                              issueDateInputRef.current?.focus();
                            }
                          }}
                          className={`w-full p-2.5 border rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${errors.bank_name ? "border-red-500" : "border-gray-300"}`}
                          required
                        />
                        {errors.bank_name && <p className="mt-1 text-xs text-red-600">{errors.bank_name}</p>}
                      </div>
                      <div>
                        <label htmlFor="issue_date" className="block mb-1 text-sm font-medium text-gray-700">
                          Issue Date <span className="text-red-500">*</span>
                        </label>
                        <input
                          ref={issueDateInputRef}
                          id="issue_date"
                          type="date"
                          value={formData.issue_date || ""}
                          onChange={e => setFormData(prev => ({ ...prev, issue_date: e.target.value }))}
                          onKeyDown={e => {
                            if (e.key === "Enter") {
                              e.preventDefault();
                              purchaseAmountRef.current?.focus();
                            }
                          }}
                          className={`w-full p-2.5 border rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${errors.issue_date ? "border-red-500" : "border-gray-300"}`}
                          required
                        />
                        {errors.issue_date && <p className="mt-1 text-xs text-red-600">{errors.issue_date}</p>}
                      </div>
                    </div>
                  )}
                  </div>
                  
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div className="flex-1 w-full">
                      <label
                        htmlFor="purchaseAmount"
                        className="block mb-1 text-sm font-medium text-gray-700"
                      >
                        Amount Paid <span className="text-red-500">*</span>
                      </label>
                      <input
                        id="purchaseAmount"
                        ref={purchaseAmountRef}
                        type="number"
                        value={formData.purchaseDetails.amount ?? 0}
                        onChange={(e) => handleInputChange(e, "purchaseDetails", "amount")}
                        onKeyDown={e => {
                          if (e.key === "Enter") {
                            e.preventDefault();
                            redeemPointsRef.current?.focus();
                          }
                        }}
                        className={`w-full p-2.5 border rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${errors.purchaseAmount ? "border-red-500" : "border-gray-300"}`}
                        min="0"
                        step="0.01"
                        placeholder="0.00"
                      />
                      {errors.purchaseAmount && (
                        <p className="mt-1 text-xs text-red-600">{errors.purchaseAmount}</p>
                      )}
                    </div>
                    <div className="flex-1">
                      <label
                        htmlFor="redeemPoints"
                        className="block mb-1 text-sm font-medium text-gray-700"
                      >
                        Redeem Points
                      </label>
                      <input
                        id="redeemPoints"
                        ref={redeemPointsRef}
                        type="number"
                        value={redeemPoints}
                        min="0"
                        max={customerLoyaltyPoints || 0}
                        onChange={e => {
                          let val = parseFloat(e.target.value) || 0;
                          if (val < 0) val = 0;
                          if (customerLoyaltyPoints !== null && val > customerLoyaltyPoints) val = customerLoyaltyPoints;
                          setRedeemPoints(val);
                          if (val > (customerLoyaltyPoints || 0)) {
                            setRedeemPointsError("Redeem amount cannot exceed available loyalty points.");
                          } else {
                            setRedeemPointsError("");
                          }
                        }}
                        onKeyDown={e => {
                          if (e.key === "Enter") {
                            e.preventDefault();
                            taxPercentageRef.current?.focus();
                          }
                        }}
                        className="w-full p-2.5 border rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 border-gray-300"
                        placeholder="0"
                      />
                      {redeemPointsError && (
                        <p className="mt-1 text-xs text-right text-red-600">{redeemPointsError}</p>
                      )}
                      <div className="mt-1 text-xs text-gray-500">(Points available: {customerLoyaltyPoints ?? 0})</div>
                    </div>
                  </div>
                  {/* <div className="mt-2 text-sm font-semibold text-blue-700">
                    Total Paid Amount: {formatCurrency((parseFloat(formData.purchaseDetails.amount) || 0) + (parseFloat(redeemPoints) || 0))}
                  </div> */}
                  <div>
                    <label
                      htmlFor="taxPercentage"
                      className="block mb-1 text-sm font-medium text-gray-700"
                    >
                      Tax (%) <span className="text-red-500">*</span>
                    </label>
                    <input
                      id="taxPercentage"
                      ref={taxPercentageRef}
                      type="number"
                      value={formData.purchaseDetails.taxPercentage ?? 0}
                      onChange={(e) =>
                        handleInputChange(e, "purchaseDetails", "taxPercentage")
                      }
                      onKeyDown={(e) => handleEnterKey(e, taxPercentageRef)}
                      className={`w-full p-2.5 border rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${errors.purchaseTaxPercentage ? "border-red-500" : "border-gray-300"}`}
                      min="0"
                      step="0.1"
                      placeholder="0.0"
                    />
                    {errors.purchaseTaxPercentage && (
                      <p className="mt-1 text-xs text-red-600">
                        {errors.purchaseTaxPercentage}
                      </p>
                    )}
                  </div>
                </div>
                  
              </div>
              <div className="p-4 bg-white border border-gray-200 rounded-lg shadow-sm">
                <h4 className="pb-2 mb-3 text-lg font-semibold text-gray-800 border-b border-gray-200">
                  Invoice Summary
                </h4>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">
                      Total (incl. Tax):
                    </span>

                    <span className="text-sm font-medium">
                      {formatCurrency(total)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Discount:</span>
                    <span className="text-sm font-medium">
                      {formatCurrency(calculateTotalDiscount())}
                    </span>
                  </div>

                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">
                      Tax ({formData.purchaseDetails.taxPercentage}%):
                    </span>
                    <span className="text-sm font-medium">
                      {formatCurrency(tax)}
                    </span>
                  </div>
                  <div className="flex justify-between pt-3 border-t border-gray-200">
                    <span className="text-base font-semibold">
                      Grand Total:
                    </span>
                    <span className="text-base font-semibold">
                      {formatCurrency(total - calculateTotalDiscount())}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Paid:</span>
                    <span className="text-sm font-medium text-green-600">
                      {formatCurrency((parseFloat(formData.purchaseDetails.amount) || 0) + (parseFloat(redeemPoints) || 0))}
                    </span>
                  </div>
                  <div className="flex justify-between pt-3 border-t border-gray-200">
                    <span className="text-sm font-medium">
                      {balanceDue > 0 ? "Balance Due:" : "Change:"}
                    </span>
                    <span
                      className={`text-sm font-medium ${
                        balanceDue > 0 ? "text-red-600" : "text-green-600"
                      }`}
                    >
                       {formatCurrency(Math.abs(balanceDue))}
                    </span>
                  </div>
                  {/* <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Balance Due:</span>
                    <span className="text-sm font-medium text-red-600">
                      {formatCurrency(Math.max(0, (total - calculateTotalDiscount()) - (parseFloat(formData.purchaseDetails.amount) || 0) + (parseFloat(redeemPoints) || 0)))}
                    </span>
                  </div> */}
                </div>
              </div>
            </div>

            <div className="sticky bottom-0 py-4 mt-6 -mx-6 bg-white border-t border-gray-200 rounded-b-xl">
              <div className="flex justify-end px-6 space-x-4">
                <button
                  type="button"
                  onClick={onCancel}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                >
                  Cancel (Esc)
                </button>
                <button
                  ref={submitButtonRef}
                  type="submit"
                  disabled={formData.items.length === 0 || loading}
                  className={`px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                    loading ? "opacity-70 cursor-not-allowed" : ""
                  }`}
                >
                  {loading ? (
                    <span className="flex items-center justify-center">
                      <svg
                        className="w-4 h-4 mr-2 animate-spin"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      Processing...
                    </span>
                  ) : isEditMode ? (
                    "Update Invoice"
                  ) : (
                    "Save Invoice"
                  )}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </ErrorBoundary>
  );
};

export default SalesInvoice;
